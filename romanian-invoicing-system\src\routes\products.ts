import { Hono } from 'hono';
import { z } from 'zod';
import { validator } from 'hono/validator';
import type { AppEnv } from '../index'; // Changed AppContext to AppEnv

const productRoutes = new Hono<AppEnv>();

// Zod validation schema for Product
const ProductSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive'), // Assuming price is numeric
  vat_rate: z.number().min(0).max(100), // VAT rate as a percentage, e.g., 19 for 19%
  unit: z.string().min(1).max(50).default('buc'), // Measurement unit, e.g., buc, kg, ora
  currency: z.string().length(3).default('RON'), // ISO currency code
  is_active: z.boolean().default(true),
  // company_id will be derived from the authenticated user's company
});

const UpdateProductSchema = ProductSchema.partial();

// Create a new product
productRoutes.post('/', validator('json', (value, c) => {
  const parsed = ProductSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const productData = c.req.valid('json');
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const result = await db.prepare(
      'INSERT INTO products (company_id, name, description, price, vat_rate, unit, currency, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
    ).bind(
      user.company_id,
      productData.name,
      productData.description,
      productData.price,
      productData.vat_rate,
      productData.unit,
      productData.currency,
      productData.is_active
    ).run();
    
    if (!result.success || result.meta.last_row_id === undefined) {
        return c.json({ error: 'Failed to create product', details: result.error || 'No last_row_id' }, 500);
    }

    const newProduct = await db.prepare('SELECT * FROM products WHERE id = ? AND company_id = ?')
                              .bind(result.meta.last_row_id, user.company_id)
                              .first();

    return c.json(newProduct, 201);
  } catch (error: any) {
    console.error('Error creating product:', error);
    return c.json({ error: 'Failed to create product', details: error.message }, 500);
  }
});

// Get all products for the company
productRoutes.get('/', async (c) => {
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const { results } = await db.prepare('SELECT * FROM products WHERE company_id = ? ORDER BY name ASC')
                                .bind(user.company_id)
                                .all();
    return c.json(results);
  } catch (error: any) {
    console.error('Error fetching products:', error);
    return c.json({ error: 'Failed to fetch products', details: error.message }, 500);
  }
});

// Get a specific product by ID
productRoutes.get('/:productId', async (c) => {
  const productId = parseInt(c.req.param('productId'));
  if (isNaN(productId)) {
    return c.json({ error: 'Invalid product ID' }, 400);
  }
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const product = await db.prepare('SELECT * FROM products WHERE id = ? AND company_id = ?')
                            .bind(productId, user.company_id)
                            .first();
    if (!product) {
      return c.json({ error: 'Product not found or access denied' }, 404);
    }
    return c.json(product);
  } catch (error: any) {
    console.error(`Error fetching product ${productId}:`, error);
    return c.json({ error: 'Failed to fetch product', details: error.message }, 500);
  }
});

// Update a product
productRoutes.put('/:productId', validator('json', (value, c) => {
  const parsed = UpdateProductSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const productId = parseInt(c.req.param('productId'));
  if (isNaN(productId)) {
    return c.json({ error: 'Invalid product ID' }, 400);
  }
  const productData = c.req.valid('json');
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  const fieldsToUpdate = Object.keys(productData).filter(key => productData[key as keyof typeof productData] !== undefined);
  if (fieldsToUpdate.length === 0) {
    return c.json({ error: 'No fields to update' }, 400);
  }
  const setClause = fieldsToUpdate.map(key => `${key} = ?`).join(', ');
  const values = fieldsToUpdate.map(key => productData[key as keyof typeof productData]);

  try {
    const result = await db.prepare(`UPDATE products SET ${setClause} WHERE id = ? AND company_id = ?`)
                            .bind(...values, productId, user.company_id)
                            .run();
    
    if (result.meta.changes === 0) {
        return c.json({ error: 'Product not found, no changes made, or access denied' }, 404);
    }

    const updatedProduct = await db.prepare('SELECT * FROM products WHERE id = ? AND company_id = ?')
                                .bind(productId, user.company_id)
                                .first();
    return c.json(updatedProduct);
  } catch (error: any) {
    console.error(`Error updating product ${productId}:`, error);
    return c.json({ error: 'Failed to update product', details: error.message }, 500);
  }
});

// Delete a product (consider soft delete: set is_active = false)
productRoutes.delete('/:productId', async (c) => {
  const productId = parseInt(c.req.param('productId'));
  if (isNaN(productId)) {
    return c.json({ error: 'Invalid product ID' }, 400);
  }
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    // Check if product is used in any non-draft/non-cancelled invoices before deleting
    // This logic might be complex depending on invoice statuses
    const invoiceItems = await db.prepare('SELECT ii.id FROM invoice_items ii JOIN invoices i ON ii.invoice_id = i.id WHERE ii.product_id = ? AND i.company_id = ? AND i.status NOT IN (\'draft\', \'cancelled\') LIMIT 1')
                                 .bind(productId, user.company_id)
                                 .first();
    if (invoiceItems) {
      return c.json({ error: 'Cannot delete product used in existing invoices. Consider deactivating it instead.' }, 400);
    }

    const result = await db.prepare('DELETE FROM products WHERE id = ? AND company_id = ?')
                            .bind(productId, user.company_id)
                            .run();

    if (result.meta.changes === 0) {
        return c.json({ error: 'Product not found or access denied' }, 404);
    }

    return c.json({ message: 'Product deleted successfully' });
  } catch (error: any) {
    console.error(`Error deleting product ${productId}:`, error);
    return c.json({ error: 'Failed to delete product', details: error.message }, 500);
  }
});

export default productRoutes;