import { WorkerEnv } from 'handmadein-shared';
import { EmailService } from './email';

export class NewsletterService {
  private env: WorkerEnv;
  private emailService: EmailService;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.emailService = new EmailService(env);
  }

  /**
   * Subscribe an email to the newsletter using Resend
   */
  async subscribe(email: string, metadata?: Record<string, any>): Promise<{ success: boolean; message: string }> {
    try {
      // Validate email format
      if (!this.isValidEmail(email)) {
        return { success: false, message: 'Adresa de email nu este validă.' };
      }

      // Add to Resend audience
      if (!this.env.RESEND_AUDIENCE_ID) {
        console.error('RESEND_AUDIENCE_ID not configured');
        return { success: false, message: 'Serviciul de newsletter nu este configurat corect.' };
      }

      if (!this.env.RESEND_API_KEY) {
        console.error('RESEND_API_KEY not configured');
        return { success: false, message: 'Serviciul de newsletter nu este configurat corect.' };
      }

      console.log('Newsletter subscription attempt for:', email.toLowerCase());
      console.log('Using audience ID:', this.env.RESEND_AUDIENCE_ID);
      console.log('API key configured:', !!this.env.RESEND_API_KEY);

      const requestBody = {
        email: email.toLowerCase(),
        unsubscribed: false
      };

      console.log('Sending request to Resend audience contacts API:', requestBody);
      console.log('API endpoint:', `https://api.resend.com/audiences/${this.env.RESEND_AUDIENCE_ID}/contacts`);

      const response = await fetch(`https://api.resend.com/audiences/${this.env.RESEND_AUDIENCE_ID}/contacts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.toLowerCase(),
          unsubscribed: false
        }),
      });

      console.log('Resend API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorData: any = {};
        
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { message: errorText };
        }
        
        console.error('Failed to add email to Resend audience:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData,
          rawResponse: errorText
        });
        
        // Check if it's a duplicate email error (already subscribed)
        if (response.status === 422 && errorData.message?.includes('already exists')) {
          return { success: false, message: 'Această adresă de email este deja abonată la newsletter.' };
        }
        
        return { success: false, message: 'A apărut o eroare la procesarea abonării. Te rugăm să încerci din nou.' };
      }

      console.log('Successfully added email to Resend audience');

      // Send welcome email using Resend with unsubscribe support
      try {
        await this.emailService.sendEmail({
          to: email,
          from: 'Newsletter <<EMAIL>>',
          subject: 'Bine ai venit la newsletterul Hanmade in RO!',
          html: this.getWelcomeEmailTemplate(email),
          text: this.getWelcomeEmailText(email)
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail the subscription if email fails - they're subscribed to the audience
      }

      return { success: true, message: 'Te-ai abonat cu succes la newsletter. Verifică-ți email-ul pentru confirmarea abonării.' };
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      return { success: false, message: 'A apărut o eroare la procesarea abonării. Te rugăm să încerci din nou.' };
    }
  }

  /**
   * Unsubscribe an email from the newsletter using Resend
   */
  async unsubscribe(email: string): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.env.RESEND_AUDIENCE_ID) {
        console.error('RESEND_AUDIENCE_ID not configured');
        return { success: false, message: 'Serviciul de newsletter nu este configurat corect.' };
      }

      // Remove from Resend audience by setting unsubscribed to true
      const response = await fetch(`https://api.resend.com/audiences/${this.env.RESEND_AUDIENCE_ID}/contacts`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.toLowerCase(),
          unsubscribed: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to unsubscribe email from Resend audience:', errorData);
        
        if (response.status === 404) {
          return { success: false, message: 'Adresa de email nu a fost găsită în lista de abonați.' };
        }
        
        return { success: false, message: 'A apărut o eroare la procesarea dezabonării. Te rugăm să încerci din nou.' };
      }

      console.log('Successfully unsubscribed email from Resend audience');
      return { success: true, message: 'Te-ai dezabonat cu succes de la newsletter.' };
    } catch (error) {
      console.error('Newsletter unsubscribe error:', error);
      return { success: false, message: 'A apărut o eroare la procesarea dezabonării. Te rugăm să încerci din nou.' };
    }
  }

  /**
   * Get subscriber statistics from Resend
   */
  async getSubscriberStats(): Promise<{
    total: number;
    subscribed: number;
    unsubscribed: number;
  }> {
    try {
      if (!this.env.RESEND_AUDIENCE_ID) {
        throw new Error('RESEND_AUDIENCE_ID not configured');
      }

      const response = await fetch(`https://api.resend.com/audiences/${this.env.RESEND_AUDIENCE_ID}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch audience stats from Resend');
      }

      const audienceData = await response.json() as any;
      
      return {
        total: audienceData.object_count || 0,
        subscribed: audienceData.object_count || 0, // Resend only shows active subscribers
        unsubscribed: 0 // Resend doesn't include unsubscribed in count
      };
    } catch (error) {
      console.error('Error getting subscriber stats:', error);
      return {
        total: 0,
        subscribed: 0,
        unsubscribed: 0
      };
    }
  }

  /**
   * Get all subscribers from Resend (admin)
   */
  async getAllSubscribers(options: {
    page?: number;
    limit?: number;
  } = {}): Promise<{ subscribers: any[]; count: number }> {
    try {
      if (!this.env.RESEND_AUDIENCE_ID) {
        throw new Error('RESEND_AUDIENCE_ID not configured');
      }

      const { page = 1, limit = 50 } = options;
      
      const response = await fetch(`https://api.resend.com/audiences/${this.env.RESEND_AUDIENCE_ID}/contacts`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch contacts from Resend');
      }

      const contactsData = await response.json() as any;
      
      // Transform Resend contacts to match our expected format
      const subscribers = (contactsData.data || []).map((contact: any) => ({
        id: contact.id,
        email: contact.email,
        subscribed_at: new Date(contact.created_at),
        status: contact.unsubscribed ? 'unsubscribed' : 'subscribed',
        metadata: {}
      }));

      return { 
        subscribers, 
        count: contactsData.data?.length || 0 
      };
    } catch (error) {
      console.error('Error getting subscribers:', error);
      return { subscribers: [], count: 0 };
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get welcome email HTML template
   */
  private getWelcomeEmailTemplate(email: string): string {
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bine ai venit la newsletterul Hanmade in RO!</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header img {
      max-width: 180px;
      margin-bottom: 20px;
    }
    h1 {
      color: #4d7c0f;
      margin-top: 0;
    }
    .content {
      background-color: #f9f9f9;
      padding: 30px;
      border-radius: 8px;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #777;
      margin-top: 30px;
    }
    .unsubscribe {
      color: #777;
      font-size: 12px;
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://hcdn.handmadein.ro/handmadeinrologo.png" alt="Hanmade in RO">
      <h1>Bine ai venit în comunitatea noastră!</h1>
    </div>
    
    <div class="content">
      <p>Bună,</p>
      
      <p>Îți mulțumim că te-ai abonat la newsletterul Hanmade in RO!</p>
      
      <p>De acum înainte, vei primi periodic:</p>
      <ul>
        <li>Noutăți despre produsele artizanale românești</li>
        <li>Povești inspiraționale ale artizanilor noștri</li>
        <li>Informații despre promoții și evenimente speciale</li>
        <li>Sfaturi și idei pentru produse handmade</li>
      </ul>
      
      <p>Suntem încântați să te avem alături în această călătorie de descoperire a creațiilor autentice românești!</p>
      
      <p>Cu drag,<br>Echipa Hanmade in RO</p>
    </div>
    
    <div class="footer">
      <p>© ${new Date().getFullYear()} Hanmade in RO. Toate drepturile rezervate.</p>
      <p>
        Nu mai dorești să primești emailuri de la noi? 
        <a href="{{{RESEND_UNSUBSCRIBE_URL}}}" class="unsubscribe">Dezabonează-te</a>
      </p>
    </div>
  </div>
</body>
</html>`;
  }

  /**
   * Get welcome email text template
   */
  private getWelcomeEmailText(email: string): string {
    return `Bine ai venit la newsletterul Hanmade in RO!

Bună,

Îți mulțumim că te-ai abonat la newsletterul Hanmade in RO!

De acum înainte, vei primi periodic:
- Noutăți despre produsele artizanale românești
- Povești inspiraționale ale artizanilor noștri
- Informații despre promoții și evenimente speciale
- Sfaturi și idei pentru produse handmade

Suntem încântați să te avem alături în această călătorie de descoperire a creațiilor autentice românești!

Cu drag,
Echipa Hanmade in RO

© ${new Date().getFullYear()} Hanmade in RO. Toate drepturile rezervate.

Nu mai dorești să primești emailuri de la noi? Dezabonează-te: {{{RESEND_UNSUBSCRIBE_URL}}}`;
  }
} 