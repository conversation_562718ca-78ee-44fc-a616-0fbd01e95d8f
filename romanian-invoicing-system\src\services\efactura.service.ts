import type { D1Database } from '@cloudflare/workers-types';
import { AnafService } from './anaf.service';
import { UblService } from './ubl.service';

// Socrate Business Services e-Factura API endpoint
const SOCRATE_EFACTURA_API_ENDPOINT = 'https://ro-efactura-api.socrate.io/graphql';

// Socrate Business Services API response interfaces
export interface SocrateInvoiceResponse {
    id: string;
    status: 'CREATED' | 'UPLOADED' | 'SIGNED' | 'REJECTED';
    requestId?: string;
    downloadId?: string;
    messages?: Array<{
        type: 'info' | 'warning' | 'error';
        details: string;
    }>;
}

export interface SocrateStatusResponse {
    status: 'CREATED' | 'UPLOADED' | 'SIGNED' | 'REJECTED';
    anafStatus?: string;
    messages?: Array<{
        type: 'info' | 'warning' | 'error';
        details: string;
    }>;
    pdfUrl?: string;
    xmlUrl?: string;
}

export interface SocrateInboundInvoice {
    id: string;
    invoiceNumber: string;
    status: string;
    supplierName: string;
    legalMonetaryTotal: {
        taxInclusiveAmount: {
            amount: number;
            currency: string;
        };
    };
}

export interface SocrateInboundInvoicesResponse {
    items: SocrateInboundInvoice[];
    nextToken?: string;
}

// Legacy interfaces for backward compatibility
export interface EFacturaUploadResponse extends SocrateInvoiceResponse {}
export interface EFacturaStatusResponse extends SocrateStatusResponse {}

export class EFacturaService {
    private db: D1Database;
    private anafService: AnafService;
    private ublService: UblService;
    private companyId: number;
    private testMode: boolean;
    private apiKey: string;

    constructor(db: D1Database, anafService: AnafService, ublService: UblService, companyId: number, testMode: boolean = true) {
        this.db = db;
        this.anafService = anafService;
        this.ublService = ublService;
        this.companyId = companyId;
        this.testMode = testMode;
        this.apiKey = ''; // Will be loaded from company settings
    }

    private async getApiKey(): Promise<string> {
        if (this.apiKey) {
            return this.apiKey;
        }

        // Load API key from company settings
        const stmt = this.db.prepare('SELECT value FROM settings WHERE company_id = ? AND key = ?');
        const result = await stmt.bind(this.companyId, 'socrate_api_key').first<{ value: string }>();

        if (!result?.value) {
            throw new Error('Socrate API key not configured for this company');
        }

        this.apiKey = result.value;
        return this.apiKey;
    }

    private async getHeaders(): Promise<HeadersInit> {
        const apiKey = await this.getApiKey();
        return {
            'x-api-key': apiKey,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    async submitInvoice(xmlContent: string, organizationId: string, isVatPayer: boolean = true): Promise<SocrateInvoiceResponse | { error: string }> {
        try {
            const headers = await this.getHeaders();

            // GraphQL mutation for sending invoice
            const mutation = `
                mutation sendInvoice($organizationId: ID!, $input: CreateInvoiceInput!) {
                    sendInvoice(organizationId: $organizationId, input: $input) {
                        id
                        status
                        requestId
                        downloadId
                        messages {
                            type
                            details
                        }
                    }
                }
            `;

            const variables = {
                organizationId: organizationId,
                input: {
                    xml: xmlContent,
                    format: 'UBL',
                    testMode: this.testMode
                }
            };

            const response = await fetch(SOCRATE_EFACTURA_API_ENDPOINT, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    query: mutation,
                    variables: variables
                })
            });

            const result = await response.json();

            if (!response.ok || result.errors) {
                console.error('Socrate e-Factura Upload Error:', result);
                return {
                    error: `e-Factura upload failed: ${result.errors ?
                        result.errors.map((e: any) => e.message).join(', ') :
                        'Unknown error'}`
                };
            }

            return result.data.sendInvoice;
        } catch (e: any) {
            console.error('e-Factura Upload Exception:', e);
            return { error: `Network error during e-Factura upload: ${e.message}` };
        }
    }

    async checkInvoiceStatus(invoiceId: string): Promise<SocrateStatusResponse | { error: string }> {
        try {
            const headers = await this.getHeaders();

            // GraphQL query for checking invoice status
            const query = `
                query getInvoiceStatus($invoiceId: ID!) {
                    invoice(id: $invoiceId) {
                        id
                        status
                        anafStatus
                        messages {
                            type
                            details
                        }
                        pdfUrl
                        xmlUrl
                    }
                }
            `;

            const variables = {
                invoiceId: invoiceId
            };

            const response = await fetch(SOCRATE_EFACTURA_API_ENDPOINT, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    query: query,
                    variables: variables
                })
            });

            const result = await response.json();

            if (!response.ok || result.errors) {
                console.error('Socrate e-Factura Status Check Error:', result);
                return {
                    error: `e-Factura status check failed: ${result.errors ?
                        result.errors.map((e: any) => e.message).join(', ') :
                        'Unknown error'}`
                };
            }

            if (!result.data.invoice) {
                return { error: 'Invoice not found' };
            }

            return result.data.invoice;
        } catch (e: any) {
            console.error('e-Factura Status Check Exception:', e);
            return { error: `Network error during e-Factura status check: ${e.message}` };
        }
    }

    async verifyXml(xmlContent: string): Promise<{ status: 'valid' | 'invalid'; errors?: string[] } | { error: string }> {
        try {
            const headers = await this.getHeaders();

            // GraphQL query for XML verification
            const query = `
                query verifyXml($xml: String!) {
                    verifyXml(xml: $xml) {
                        status
                        errors
                    }
                }
            `;

            const variables = {
                xml: xmlContent
            };

            const response = await fetch(SOCRATE_EFACTURA_API_ENDPOINT, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    query: query,
                    variables: variables
                })
            });

            const result = await response.json();

            if (!response.ok || result.errors) {
                console.error('Socrate XML Verification Error:', result);
                return {
                    error: `XML verification failed: ${result.errors ?
                        result.errors.map((e: any) => e.message).join(', ') :
                        'Unknown error'}`
                };
            }

            return result.data.verifyXml;
        } catch (e: any) {
            console.error('XML Verification Exception:', e);
            return { error: `Network error during XML verification: ${e.message}` };
        }
    }

    async getInboundInvoices(organizationId: string, filter: any = {}): Promise<SocrateInboundInvoicesResponse | { error: string }> {
        try {
            const headers = await this.getHeaders();

            // GraphQL query for inbound invoices
            const query = `
                query inboundInvoices($organizationId: ID!, $filter: InboundInvoicesFilter!) {
                    inboundInvoices(organizationId: $organizationId, filter: $filter) {
                        items {
                            id
                            invoiceNumber
                            status
                            supplierName
                            legalMonetaryTotal {
                                taxInclusiveAmount {
                                    amount
                                    currency
                                }
                            }
                        }
                        nextToken
                    }
                }
            `;

            const variables = {
                organizationId: organizationId,
                filter: filter
            };

            const response = await fetch(SOCRATE_EFACTURA_API_ENDPOINT, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    query: query,
                    variables: variables
                })
            });

            const result = await response.json();

            if (!response.ok || result.errors) {
                console.error('Socrate Inbound Invoices Error:', result);
                return {
                    error: `Inbound invoices fetch failed: ${result.errors ?
                        result.errors.map((e: any) => e.message).join(', ') :
                        'Unknown error'}`
                };
            }

            return result.data.inboundInvoices;
        } catch (e: any) {
            console.error('Inbound Invoices Exception:', e);
            return { error: `Network error during inbound invoices fetch: ${e.message}` };
        }
    }

    // Legacy method names for backward compatibility
    async downloadResponseMessage(downloadId: string): Promise<string | { error: string }> {
        const status = await this.checkInvoiceStatus(downloadId);
        if ('error' in status) {
            return status;
        }

        if (status.xmlUrl) {
            try {
                const response = await fetch(status.xmlUrl);
                if (response.ok) {
                    return await response.text();
                }
                return { error: 'Failed to download XML from URL' };
            } catch (e: any) {
                return { error: `Download error: ${e.message}` };
            }
        }

        return { error: 'No download URL available' };
    }

    async listMessages(organizationId: string, days: number = 30): Promise<SocrateInboundInvoicesResponse | { error: string }> {
        const filter = {
            days: days
        };
        return this.getInboundInvoices(organizationId, filter);
    }
}