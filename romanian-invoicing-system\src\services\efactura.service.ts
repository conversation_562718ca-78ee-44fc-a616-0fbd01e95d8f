import type { D1Database } from '@cloudflare/workers-types';
import { AnafService } from './anaf.service';
import { UblService } from './ubl.service'; // For UBL generation if needed, or parsing responses

// Define e-Factura specific API endpoints (these are examples, actual URLs must be verified)
const EFACTURA_API_URLS = {
    test: {
        upload: 'https://api.anaf.ro/test/efactura/rest/upload', // Example URL
        status: 'https://api.anaf.ro/test/efactura/rest/stareMesaj', // Example URL
        download: 'https://api.anaf.ro/test/efactura/rest/descarcare', // Example URL
        listMessages: 'https://api.anaf.ro/test/efactura/rest/listaMesajeFactura', // Example URL
    },
    production: {
        upload: 'https://api.anaf.ro/prod/efactura/rest/upload', // Example URL
        status: 'https://api.anaf.ro/prod/efactura/rest/stareMesaj', // Example URL
        download: 'https://api.anaf.ro/prod/efactura/rest/descarcare', // Example URL
        listMessages: 'https://api.anaf.ro/prod/efactura/rest/listaMesajeFactura', // Example URL
    }
};

export interface EFacturaUploadResponse {
    index_incarcare?: string;
    errors?: Array<{ id_incarcare: string; errors: string }>;
    // ANAF may return other fields
}

export interface EFacturaStatusResponse {
    stare: string; // e.g., "OK", "EROARE", "IN_PRELUCRARE"
    id_incarcare: string;
    detalii?: string;
    data_prelucrare?: string;
    erori?: string; // XML or text with error details
    id_descarcare_raspuns?: string;
    // Other fields as per ANAF documentation
}

interface EFacturaMessage {
    id: string; // ID-ul mesajului (pentru descarcare)
    cif_emitent: string;
    tip: string; // e.g., "FACTURA_TRIMISA", "RASPUNS_EROARE", "RASPUNS_OK"
    detalii: string;
    data_creare: string; // Format YYYYMMDD
    id_incarcare?: string;
    // Other fields
}

interface EFacturaListMessagesResponse {
    mesaje?: EFacturaMessage[];
    erori?: string;
    // Other fields
}

export class EFacturaService {
    private db: D1Database;
    private anafService: AnafService;
    private ublService: UblService;
    private companyId: number;
    private testMode: boolean;

    constructor(db: D1Database, anafService: AnafService, ublService: UblService, companyId: number, testMode: boolean = true) {
        this.db = db;
        this.anafService = anafService;
        this.ublService = ublService; // May not be directly used if XML is pre-generated
        this.companyId = companyId;
        this.testMode = testMode;
    }

    private getApiUrls() {
        return this.testMode ? EFACTURA_API_URLS.test : EFACTURA_API_URLS.production;
    }

    private async getHeaders(): Promise<HeadersInit | { error: string }> {
        const accessToken = await this.anafService.getValidAccessToken();
        if (!accessToken) {
            return { error: 'Failed to obtain ANAF access token for e-Factura operation.' };
        }
        return {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/xml', // For upload
            'Accept': 'application/json', // For status/list, or XML for download
        };
    }

    async submitInvoice(xmlContent: string, cif: string, isVatPayer: boolean): Promise<EFacturaUploadResponse | { error: string }> {
        const headers = await this.getHeaders();
        if ('error' in headers) return headers;

        const apiUrl = this.getApiUrls().upload;
        const uploadUrl = new URL(apiUrl);
        // ANAF requires CIF and if VAT payer as query params for upload
        uploadUrl.searchParams.append('cif', cif);
        uploadUrl.searchParams.append('standard', 'UBL'); // Or whatever standard is used
        uploadUrl.searchParams.append('extern', 'false'); // Or true if applicable
        // uploadUrl.searchParams.append('platitor_tva', isVatPayer ? 'true' : 'false'); // Check exact param name

        try {
            const response = await fetch(uploadUrl.toString(), {
                method: 'POST',
                headers: { ...headers, 'Content-Type': 'application/xml' }, // Ensure correct content type for XML
                body: xmlContent,
            });

            const responseData: EFacturaUploadResponse = await response.json(); // ANAF usually responds with JSON
            if (!response.ok) {
                console.error('e-Factura Upload Error:', responseData);
                return { error: `e-Factura upload failed: ${JSON.stringify(responseData.errors || responseData)}` };
            }
            return responseData;
        } catch (e: any) {
            console.error('e-Factura Upload Exception:', e);
            return { error: `Network error during e-Factura upload: ${e.message}` };
        }
    }

    async checkInvoiceStatus(uploadIndex: string): Promise<EFacturaStatusResponse | { error: string }> {
        const headers = await this.getHeaders();
        if ('error' in headers) return headers as { error: string };

        const apiUrl = this.getApiUrls().status;
        const statusUrl = new URL(apiUrl);
        statusUrl.searchParams.append('id_incarcare', uploadIndex);

        try {
            const response = await fetch(statusUrl.toString(), {
                method: 'GET',
                headers: { ...headers, 'Accept': 'application/json' }, // Expect JSON response for status
            });

            // const responseData: EFacturaStatusResponse = await response.json(); // Original line
            const jsonData: any = await response.json(); // Parse as any first

            if (!response.ok) {
                console.error('e-Factura Status Check Error:', jsonData);
                // Attempt to create a more specific error message if possible
                const errorDetail = jsonData.detalii || (jsonData.errors && jsonData.errors.length > 0 && jsonData.errors[0].errors) ? 
                                  (jsonData.errors[0].errors) : 
                                  (jsonData.erori || JSON.stringify(jsonData));
                return { error: `e-Factura status check failed: ${errorDetail}` };
            }
            
            // If response is ok, cast to the expected type and validate essential fields
            const responseData: EFacturaStatusResponse = jsonData as EFacturaStatusResponse;
            if (typeof responseData.stare === 'undefined' || typeof responseData.id_incarcare === 'undefined') {
                 console.error('e-Factura Status Check Error: Response OK but missing required fields', responseData);
                 return { error: 'e-Factura status check response is malformed despite OK status.' };
            }
            return responseData;
        } catch (e: any) {
            console.error('e-Factura Status Check Exception:', e);
            return { error: `Network error during e-Factura status check: ${e.message}` };
        }
    }

    async downloadResponseMessage(downloadId: string): Promise<string | { error: string }> {
        const headers = await this.getHeaders();
        if ('error' in headers) return headers as { error: string };

        const apiUrl = this.getApiUrls().download;
        const downloadUrl = new URL(apiUrl);
        downloadUrl.searchParams.append('id', downloadId); // Or 'id_descarcare'

        try {
            const response = await fetch(downloadUrl.toString(), {
                method: 'GET',
                headers: { ...headers, 'Accept': 'application/xml, application/json' }, // Accept both XML and JSON
            });

            const contentType = response.headers.get('content-type');

            if (!response.ok) {
                let errorText = `e-Factura download failed with status ${response.status}`;
                try {
                    if (contentType && contentType.includes('application/json')) {
                        const errorJson: any = await response.json();
                        errorText = `e-Factura download failed: ${errorJson.detalii || errorJson.erori || JSON.stringify(errorJson)}`;
                    } else {
                        errorText = `e-Factura download failed: ${await response.text()}`;
                    }
                } catch (e) {
                    // Fallback if parsing error body fails
                    errorText = `e-Factura download failed with status ${response.status} and could not parse error body.`;
                }
                console.error('e-Factura Download Error:', errorText);
                return { error: errorText };
            }

            // If response is OK, expect XML
            if (contentType && contentType.includes('application/xml')) {
                const xmlResponse = await response.text();
                return xmlResponse;
            } else if (contentType && contentType.includes('application/json')) {
                // This case should ideally not happen for a successful download, but handle defensively
                const jsonResponse: any = await response.json();
                console.warn('e-Factura Download: Received JSON for a successful download request', jsonResponse);
                return { error: 'Received unexpected JSON response for a download request that was marked OK.' };
            } else {
                // Fallback for unexpected content type
                const textResponse = await response.text();
                console.warn('e-Factura Download: Received unexpected content type', contentType, textResponse);
                return { error: `Received unexpected content type '${contentType}' during e-Factura download.` };
            }

        } catch (e: any) {
            console.error('e-Factura Download Exception:', e);
            return { error: `Network error during e-Factura download: ${e.message}` };
        }
    }

    async listMessages(cif: string, days: number = 30): Promise<EFacturaListMessagesResponse | { error: string }> {
        const headers = await this.getHeaders();
        if ('error' in headers) return headers;

        const apiUrl = this.getApiUrls().listMessages;
        const listUrl = new URL(apiUrl);
        listUrl.searchParams.append('cif', cif);
        listUrl.searchParams.append('zile', days.toString());

        try {
            const response = await fetch(listUrl.toString(), {
                method: 'GET',
                headers: { ...headers, 'Accept': 'application/json' },
            });

            const responseData: EFacturaListMessagesResponse = await response.json();
            if (!response.ok) {
                console.error('e-Factura List Messages Error:', responseData);
                return { error: `e-Factura list messages failed: ${JSON.stringify(responseData)}` };
            }
            return responseData;
        } catch (e: any) {
            console.error('e-Factura List Messages Exception:', e);
            return { error: `Network error during e-Factura list messages: ${e.message}` };
        }
    }
}