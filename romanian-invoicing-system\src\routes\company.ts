import { Hono } from 'hono';
import { z } from 'zod';
import { validator } from 'hono/validator';
import type { AppEnv } from '../index'; // Changed AppContext to AppEnv
import { CompanyService } from '../services/company.service';

const companyRoutes = new Hono<AppEnv>();

// Zod validation schemas
// Schema for creating a company (subset of full company details, owner_user_id will be set by service/auth context)
const CreateCompanySchema = z.object({
  name: z.string().min(1).max(255),
  cif: z.string().min(1).max(20), // Romanian Fiscal Identification Code
  reg_com: z.string().max(30).optional(), // Trade Registry Number
  address: z.string().max(255).optional(),
  city: z.string().max(100).optional(),
  county: z.string().max(100).optional(),
  country: z.string().max(50).default('Romania'),
  bank_name: z.string().max(100).optional(),
  iban: z.string().max(34).optional(), // IBAN can be up to 34 chars
  email: z.string().email().optional(),
  phone: z.string().max(20).optional(),
  // ANAF/eFactura specific fields can be added here or managed separately
  anaf_client_id: z.string().optional(),
  anaf_client_secret: z.string().optional(),
  // owner_user_id will be handled by the service based on authenticated user
});

// Schema for updating a company (all fields optional)
const UpdateCompanySchema = CreateCompanySchema.partial();

const CreateInvoiceSeriesSchema = z.object({
  series_name: z.string().min(1).max(50), // e.g., "Facturi A", "Proforme B"
  current_number: z.number().int().min(0).default(1), // Starting number for the series
  is_default: z.boolean().optional().default(false),
  // company_id will be from authenticated user context
});

const UpdateInvoiceSeriesSchema = CreateInvoiceSeriesSchema.partial().extend({
    is_default: z.boolean().optional()
});

// --- Company Management Endpoints (typically admin or first-time setup) ---

// Get current company details (for the authenticated user's company)
companyRoutes.get('/', async (c) => {
  const companyService = c.get('companyService');
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
  }

  try {
    const company = await companyService.getCompanyById(user.company_id);
    if (!company || 'error' in company) {
      return c.json({ error: 'Company not found' }, 404);
    }
    return c.json(company);
  } catch (error: any) {
    console.error('Error fetching company details:', error);
    return c.json({ error: 'Failed to fetch company details', details: error.message }, 500);
  }
});

// Update current company details
companyRoutes.put('/', validator('json', (value, c) => {
  const parsed = UpdateCompanySchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const companyData = c.req.valid('json');
  const companyService = c.get('companyService');
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
  }

  try {
    const updatedCompany = await companyService.updateCompany(user.company_id, companyData);
    if (!updatedCompany || 'error' in updatedCompany) {
      return c.json(updatedCompany || { error: 'Failed to update company' }, 400);
    }
    return c.json(updatedCompany);
  } catch (error: any) {
    console.error('Error updating company details:', error);
    return c.json({ error: 'Failed to update company details', details: error.message }, 500);
  }
});

// --- Invoice Series Management ---

// List invoice series for the company
companyRoutes.get('/invoice-series', async (c) => {
  const companyService = c.get('companyService');
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
  }

  try {
    const series = await companyService.getInvoiceSeries(user.company_id);
    if ('error' in series) {
        return c.json(series, 400);
    }
    return c.json(series);
  } catch (error: any) {
    console.error('Error listing invoice series:', error);
    return c.json({ error: 'Failed to list invoice series', details: error.message }, 500);
  }
});

// Create a new invoice series
companyRoutes.post('/invoice-series', validator('json', (value, c) => {
  const parsed = CreateInvoiceSeriesSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const seriesData = c.req.valid('json');
  const companyService = c.get('companyService');
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
  }

  try {
    // Add company_id to the series data
    const seriesDataWithCompany = { ...seriesData, company_id: user.company_id };
    const newSeries = await companyService.createInvoiceSeries(seriesDataWithCompany);
    if ('error' in newSeries) {
      return c.json(newSeries, 400);
    }
    return c.json(newSeries, 201);
  } catch (error: any) {
    console.error('Error creating invoice series:', error);
    return c.json({ error: 'Failed to create invoice series', details: error.message }, 500);
  }
});

// Get a specific invoice series
companyRoutes.get('/invoice-series/:seriesId', async (c) => {
    const seriesId = parseInt(c.req.param('seriesId'));
    if (isNaN(seriesId)) {
        return c.json({ error: 'Invalid series ID' }, 400);
    }
    const companyService = c.get('companyService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
    }

    try {
        const series = await companyService.getInvoiceSeriesById(user.company_id, seriesId);
        if (!series || 'error' in series) {
            return c.json({ error: 'Invoice series not found or access denied' }, 404);
        }
        return c.json(series);
    } catch (error: any) {
        console.error(`Error fetching invoice series ${seriesId}:`, error);
        return c.json({ error: 'Failed to fetch invoice series', details: error.message }, 500);
    }
});

// Update an invoice series
companyRoutes.put('/invoice-series/:seriesId', validator('json', (value, c) => {
    const parsed = UpdateInvoiceSeriesSchema.safeParse(value);
    if (!parsed.success) {
        return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
    }
    return parsed.data;
}), async (c) => {
    const seriesId = parseInt(c.req.param('seriesId'));
    if (isNaN(seriesId)) {
        return c.json({ error: 'Invalid series ID' }, 400);
    }
    const seriesData = c.req.valid('json');
    const companyService = c.get('companyService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
    }

    try {
        const updatedSeries = await companyService.updateInvoiceSeries(user.company_id, seriesId, seriesData);
        if ('error' in updatedSeries) {
            return c.json(updatedSeries, updatedSeries.error === 'Series not found or access denied' ? 404 : 400);
        }
        return c.json(updatedSeries);
    } catch (error: any) {
        console.error(`Error updating invoice series ${seriesId}:`, error);
        return c.json({ error: 'Failed to update invoice series', details: error.message }, 500);
    }
});

// Delete an invoice series (consider if this should be a soft delete or restricted)
companyRoutes.delete('/invoice-series/:seriesId', async (c) => {
    const seriesId = parseInt(c.req.param('seriesId'));
    if (isNaN(seriesId)) {
        return c.json({ error: 'Invalid series ID' }, 400);
    }
    const companyService = c.get('companyService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
    }

    try {
        const result = await companyService.deleteInvoiceSeries(user.company_id, seriesId);
        if ('error' in result) {
            return c.json(result, result.error === 'Series not found or access denied' || result.error === 'Cannot delete series with existing invoices' ? 400 : 404);
        }
        return c.json({ message: 'Invoice series deleted successfully' });
    } catch (error: any) {
        console.error(`Error deleting invoice series ${seriesId}:`, error);
        return c.json({ error: 'Failed to delete invoice series', details: error.message }, 500);
    }
});

// Endpoint to get the next invoice number for a series (useful for UI)
companyRoutes.get('/invoice-series/:seriesId/next-number', async (c) => {
    const seriesId = parseInt(c.req.param('seriesId'));
    if (isNaN(seriesId)) {
        return c.json({ error: 'Invalid series ID' }, 400);
    }
    const companyService = c.get('companyService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'User not associated with a company or not authenticated' }, 401);
    }

    try {
        // First get the series to find its name
        const series = await companyService.getInvoiceSeriesById(user.company_id, seriesId);
        if (!series || 'error' in series) {
            return c.json({ error: 'Invoice series not found or access denied' }, 404);
        }

        const result = await companyService.getNextInvoiceNumber(user.company_id, series.series_name);
        if ('error' in result) {
            return c.json(result, 400);
        }
        return c.json(result);
    } catch (error: any) {
        console.error(`Error getting next invoice number for series ${seriesId}:`, error);
        return c.json({ error: 'Failed to get next invoice number', details: error.message }, 500);
    }
});


export default companyRoutes;