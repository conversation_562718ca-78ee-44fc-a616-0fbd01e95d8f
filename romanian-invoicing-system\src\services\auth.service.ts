// Service for handling authentication and authorization
// This could integrate with Cloudflare Access, JWT, or other auth mechanisms.

// Import the User type from index.ts to maintain consistency
import type { User } from '../index';

export interface Session {
  user: User;
  token: string; // JWT or session token
  expiresAt: Date;
}

// These would be configured in your Worker environment
const JWT_SECRET_PLACEHOLDER = 'YOUR_VERY_SECRET_JWT_KEY_REPLACE_IN_PROD';
const TOKEN_EXPIRY_SECONDS = 3600 * 8; // 8 hours

export class AuthService {
  constructor(private readonly jwtSecret: string) {
    if (!jwtSecret) {
        throw new Error("JWT_SECRET is not provided to AuthService.");
    }
  }

  /**
   * Authenticates a user based on credentials (e.g., email/password, OAuth token).
   * This is a highly simplified placeholder.
   */
  async login(credentials: { email?: string; password?: string; oauthToken?: string }): Promise<Session | null> {
    console.log('Attempting login with credentials:', credentials.email ? { email: credentials.email } : { oauth: 'token_provided' });
    // TODO: Implement actual authentication logic
    // 1. If email/password: Validate against a user database (e.g., D1 table for users)
    //    - Hash password securely (e.g., Argon2, bcrypt)
    // 2. If OAuth token: Validate with the OAuth provider
    // 3. If Cloudflare Access: The identity might already be available in the request headers (CF-Access-Authenticated-User-Email)

    // Placeholder: Simulate successful login for a known user
    if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
      const user: User = {
        id: 1,
        email: '<EMAIL>',
        company_id: 1, // Assume company ID 1 for this example user
      };
      const token = await this.generateJwt(user as any);
      const expiresAt = new Date(Date.now() + TOKEN_EXPIRY_SECONDS * 1000);
      return { user, token, expiresAt };
    }

    // Placeholder for Cloudflare Access (if email is passed via header)
    if (credentials.email && !credentials.password && !credentials.oauthToken) {
        // This scenario implies CF Access might have pre-authenticated
        // You'd fetch user details from your DB based on credentials.email (which is CF-Access-Authenticated-User-Email)
        console.log(`Potential CF Access user: ${credentials.email}. Fetching details...`);
        // Simulate fetching user from DB
        if (credentials.email === '<EMAIL>') {
            const user: User = {
                id: 2,
                email: '<EMAIL>',
                company_id: 2,
            };
            const token = await this.generateJwt(user as any);
            const expiresAt = new Date(Date.now() + TOKEN_EXPIRY_SECONDS * 1000);
            return { user, token, expiresAt };
        }
    }

    console.warn('Login failed: Invalid credentials or method.');
    return null;
  }

  /**
   * Verifies a JWT token and returns the decoded user payload.
   * This requires a JWT library compatible with Cloudflare Workers (e.g., @tsndr/cloudflare-worker-jwt).
   */
  async verifyToken(token: string): Promise<User | null> {
    console.log('Verifying token...');
    // TODO: Implement actual JWT verification using a library
    // Example using a hypothetical jose-like library (actual implementation will vary)
    /*
    try {
      const { payload } = await jwtVerify(token, new TextEncoder().encode(JWT_SECRET_PLACEHOLDER), {
        algorithms: ['HS256'], // Specify your algorithm
      });
      return payload as User; // Adjust based on your payload structure
    } catch (error) {
      console.error('Token verification failed:', error);
      return null;
    }
    */

    // Placeholder: Simulate token verification
    if (token.startsWith('dummy_jwt_')) {
      try {
        const payloadPart = token.substring('dummy_jwt_'.length);
        const decodedUser = JSON.parse(atob(payloadPart)); // Super simple decode for placeholder
        // In real JWT, you'd verify signature and expiry
        if (decodedUser && decodedUser.id && decodedUser.companyId && decodedUser.exp > Date.now() / 1000) {
            return decodedUser as User;
        }
      } catch (e) {
        console.error('Dummy token parsing error', e);
        return null;
      }
    }
    console.warn('Token verification failed or token is invalid/expired.');
    return null;
  }

  /**
   * Generates a JWT for a given user.
   * Requires a JWT library.
   */
  private async generateJwt(user: User): Promise<string> {
    // TODO: Implement actual JWT generation using a library
    // Example using a hypothetical jose-like library:
    /*
    const secret = new TextEncoder().encode(JWT_SECRET_PLACEHOLDER);
    const jwt = await new SignJWT({ ...user })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(`${TOKEN_EXPIRY_SECONDS}s`)
      .sign(secret);
    return jwt;
    */

    // Placeholder: Simulate JWT generation
    const payload = { ...user, iat: Math.floor(Date.now() / 1000), exp: Math.floor(Date.now() / 1000) + TOKEN_EXPIRY_SECONDS };
    // Super simple encode for placeholder, NOT SECURE FOR REAL USE
    return `dummy_jwt_${btoa(JSON.stringify(payload))}`;
  }

  /**
   * Checks if a user has the required role(s).
   * Note: The current User type doesn't have roles, so this always returns true for now
   */
  hasRole(user: User, requiredRoles: string | string[]): boolean {
    if (!user) return false;
    // TODO: Implement role checking when User type includes roles
    return true; // For now, allow all authenticated users
  }

  async loginUser(credentials: { email?: string; password?: string; }): Promise<User | { error: string }> {
    // In a real app, this would involve database checks and password hashing
    if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
      return { id: 1, email: '<EMAIL>', company_id: 1 };
    }
    return { error: 'Invalid credentials' };
  }

  async registerUser(credentials: { email?: string; password?: string; }): Promise<User | { error: string }> {
    // In a real app, this would involve creating a new user in the database
    if (credentials.email && credentials.password) {
      return { id: 2, email: credentials.email, company_id: 2 };
    }
    return { error: 'Invalid registration data' };
  }

  async generateToken(user: User, expiresIn: string): Promise<string> {
    return this.generateJwt(user as any);
  }
}


// Example usage in a middleware:
// async function authMiddleware(request: Request, env: Env, userRoles: string[]): Promise<User | Response> {
//   const authHeader = request.headers.get('Authorization');
//   if (!authHeader || !authHeader.startsWith('Bearer ')) {
//     return new Response('Unauthorized: Missing or invalid token', { status: 401 });
//   }
//   const token = authHeader.substring(7);
//   const user = await authService.verifyToken(token);
//   if (!user) {
//     return new Response('Unauthorized: Invalid or expired token', { status: 401 });
//   }
//   if (!authService.hasRole(user, userRoles)) {
//     return new Response('Forbidden: Insufficient permissions', { status: 403 });
//   }
//   return user; // Pass user to the next handler
// }