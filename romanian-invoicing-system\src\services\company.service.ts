// Service for managing company-specific settings and data
import { D1Database } from '@cloudflare/workers-types';

// Corresponds to the 'companies' table in your schema.sql
export interface Company {
  id?: number;
  name: string;
  cif: string; // VAT ID / CUI
  trade_register_number: string; // Nr. Reg. Com. (J...)
  address: string;
  city: string;
  county: string; // Judet
  postal_code?: string;
  country: string; // e.g., 'RO'
  iban?: string;
  bank_name?: string;
  email?: string;
  phone?: string;
  logo_url?: string;
  // ANAF/eFactura specific settings
  anaf_client_id?: string; // Store securely, ideally in secrets manager
  anaf_client_secret?: string; // Store securely
  anaf_redirect_uri?: string;
  anaf_spv_access_token?: string; // Current access token, store securely
  anaf_spv_refresh_token?: string; // Refresh token, store securely
  anaf_spv_token_expires_at?: string; // ISO Date string
  efactura_socrate_api_key?: string; // If using Socrate, store securely
  // Default settings for invoices
  default_currency?: string; // e.g., 'RON'
  default_vat_rate?: number; // e.g., 19
  // ... other company settings
  created_at?: string;
  updated_at?: string;
}

// For invoice series, corresponds to 'invoice_series' table
export interface InvoiceSeries {
  id?: number;
  company_id: number;
  series_name: string; // e.g., 'ABC', 'XYZ'
  current_number: number;
  is_default?: boolean;
  // Potentially add format for number padding, etc.
}

export class CompanyService {
  private db: D1Database;

  constructor(db: D1Database) {
    this.db = db;
  }

  async createCompany(companyData: Omit<Company, 'id' | 'created_at' | 'updated_at'>): Promise<Company> {
    console.log('Creating company:', companyData.name);

    try {
      const stmt = this.db.prepare(`
        INSERT INTO companies (
          name, cui, reg_com, address, city, county, county_code, postal_code,
          country, phone, email, bank_account, bank_name, vat_registered, vat_rate,
          legal_representative, fiscal_representative, authorized_person, logo_path, signature_path
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = await stmt.bind(
        companyData.name,
        companyData.cif,
        companyData.trade_register_number || null,
        companyData.address,
        companyData.city,
        companyData.county,
        companyData.postal_code || null,
        companyData.postal_code || null,
        companyData.country || 'România',
        companyData.phone || null,
        companyData.email || null,
        companyData.iban || null,
        companyData.bank_name || null,
        companyData.default_vat_rate ? true : false,
        companyData.default_vat_rate || 19.00,
        null, // legal_representative
        null, // fiscal_representative
        null, // authorized_person
        companyData.logo_url || null,
        null  // signature_path
      ).run();

      if (!result.success) {
        throw new Error(`Failed to create company: ${result.error}`);
      }

      const newCompanyId = result.meta.last_row_id;
      if (!newCompanyId) {
        throw new Error('Failed to get new company ID');
      }

      // Create default invoice series for the new company
      await this.createDefaultInvoiceSeries(Number(newCompanyId));

      // Fetch and return the created company
      const newCompany = await this.getCompanyById(Number(newCompanyId));
      if (!newCompany) {
        throw new Error('Failed to retrieve created company');
      }

      return newCompany;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  async getCompanyById(companyId: number): Promise<Company | null> {
    console.log(`Getting company by ID: ${companyId}`);

    try {
      const stmt = this.db.prepare('SELECT * FROM companies WHERE id = ?');
      const company = await stmt.bind(companyId).first<any>();

      if (!company) {
        return null;
      }

      // Map database fields to Company interface
      return {
        id: company.id,
        name: company.name,
        cif: company.cui, // Database uses 'cui', interface uses 'cif'
        trade_register_number: company.reg_com,
        address: company.address,
        city: company.city,
        county: company.county,
        postal_code: company.postal_code,
        country: company.country,
        phone: company.phone,
        email: company.email,
        iban: company.bank_account,
        bank_name: company.bank_name,
        logo_url: company.logo_path,
        default_currency: 'RON', // Default value
        default_vat_rate: company.vat_rate,
        created_at: company.created_at,
        updated_at: company.updated_at,
      };
    } catch (error) {
      console.error('Error fetching company:', error);
      throw error;
    }
  }

  async updateCompany(companyId: number, updateData: Partial<Omit<Company, 'id' | 'created_at' | 'updated_at'>>): Promise<Company | null> {
    console.log(`Updating company ${companyId}:`, updateData);

    try {
      const existingCompany = await this.getCompanyById(companyId);
      if (!existingCompany) return null;

      // Build dynamic update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updateData.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(updateData.name);
      }
      if (updateData.cif !== undefined) {
        updateFields.push('cui = ?');
        updateValues.push(updateData.cif);
      }
      if (updateData.trade_register_number !== undefined) {
        updateFields.push('reg_com = ?');
        updateValues.push(updateData.trade_register_number);
      }
      if (updateData.address !== undefined) {
        updateFields.push('address = ?');
        updateValues.push(updateData.address);
      }
      if (updateData.city !== undefined) {
        updateFields.push('city = ?');
        updateValues.push(updateData.city);
      }
      if (updateData.county !== undefined) {
        updateFields.push('county = ?');
        updateValues.push(updateData.county);
      }
      if (updateData.postal_code !== undefined) {
        updateFields.push('postal_code = ?');
        updateValues.push(updateData.postal_code);
      }
      if (updateData.country !== undefined) {
        updateFields.push('country = ?');
        updateValues.push(updateData.country);
      }
      if (updateData.phone !== undefined) {
        updateFields.push('phone = ?');
        updateValues.push(updateData.phone);
      }
      if (updateData.email !== undefined) {
        updateFields.push('email = ?');
        updateValues.push(updateData.email);
      }
      if (updateData.iban !== undefined) {
        updateFields.push('bank_account = ?');
        updateValues.push(updateData.iban);
      }
      if (updateData.bank_name !== undefined) {
        updateFields.push('bank_name = ?');
        updateValues.push(updateData.bank_name);
      }
      if (updateData.default_vat_rate !== undefined) {
        updateFields.push('vat_rate = ?');
        updateValues.push(updateData.default_vat_rate);
      }
      if (updateData.logo_url !== undefined) {
        updateFields.push('logo_path = ?');
        updateValues.push(updateData.logo_url);
      }

      if (updateFields.length === 0) {
        return existingCompany; // No updates to perform
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = ?');
      updateValues.push(new Date().toISOString());
      updateValues.push(companyId); // For WHERE clause

      const sql = `UPDATE companies SET ${updateFields.join(', ')} WHERE id = ?`;
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...updateValues).run();

      if (!result.success) {
        throw new Error(`Failed to update company: ${result.error}`);
      }

      // Return updated company
      return await this.getCompanyById(companyId);
    } catch (error) {
      console.error('Error updating company:', error);
      throw error;
    }
  }

  // Helper method to create default invoice series for new companies
  private async createDefaultInvoiceSeries(companyId: number): Promise<void> {
    const defaultSeries = [
      { series: 'FACT', type: 'invoice', current_number: 0, year: new Date().getFullYear(), active: true },
      { series: 'PROF', type: 'proforma', current_number: 0, year: new Date().getFullYear(), active: true },
      { series: 'AVIZ', type: 'delivery_note', current_number: 0, year: new Date().getFullYear(), active: true },
      { series: 'CHIT', type: 'receipt', current_number: 0, year: new Date().getFullYear(), active: true }
    ];

    for (const series of defaultSeries) {
      const stmt = this.db.prepare(`
        INSERT INTO invoice_series (company_id, series, type, current_number, year, active)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      await stmt.bind(
        companyId,
        series.series,
        series.type,
        series.current_number,
        series.year,
        series.active
      ).run();
    }
  }

  // --- Invoice Series Management ---

  async createInvoiceSeries(seriesData: Omit<InvoiceSeries, 'id'>): Promise<InvoiceSeries> {
    console.log(`Creating invoice series for company ${seriesData.company_id}: ${seriesData.series_name}`);

    try {
      const stmt = this.db.prepare(`
        INSERT INTO invoice_series (company_id, series, type, current_number, year, active)
        VALUES (?, ?, 'invoice', ?, ?, ?)
      `);

      const result = await stmt.bind(
        seriesData.company_id,
        seriesData.series_name,
        seriesData.current_number || 0,
        new Date().getFullYear(),
        true
      ).run();

      if (!result.success) {
        throw new Error(`Failed to create invoice series: ${result.error}`);
      }

      const newSeriesId = result.meta.last_row_id;
      if (!newSeriesId) {
        throw new Error('Failed to get new series ID');
      }

      return {
        id: Number(newSeriesId),
        company_id: seriesData.company_id,
        series_name: seriesData.series_name,
        current_number: seriesData.current_number || 0,
        is_default: seriesData.is_default || false
      };
    } catch (error) {
      console.error('Error creating invoice series:', error);
      throw error;
    }
  }

  async getInvoiceSeries(companyId: number): Promise<InvoiceSeries[]> {
    console.log(`Getting invoice series for company ${companyId}`);

    try {
      const stmt = this.db.prepare('SELECT * FROM invoice_series WHERE company_id = ? AND active = 1 ORDER BY series');
      const { results } = await stmt.bind(companyId).all();

      return results.map((row: any) => ({
        id: row.id,
        company_id: row.company_id,
        series_name: row.series,
        current_number: row.current_number,
        is_default: false // We'll determine default logic separately if needed
      }));
    } catch (error) {
      console.error('Error fetching invoice series:', error);
      throw error;
    }
  }

  async getNextInvoiceNumber(companyId: number, seriesName?: string): Promise<{ series: string; number: number; formattedNumber: string }> {
    console.log(`Getting next invoice number for company ${companyId}, series ${seriesName || 'default'}`);

    try {
      // Find the target series
      let targetSeriesQuery: string;
      let targetSeriesParams: any[];

      if (seriesName) {
        targetSeriesQuery = 'SELECT * FROM invoice_series WHERE company_id = ? AND series = ? AND active = 1';
        targetSeriesParams = [companyId, seriesName];
      } else {
        // Get the first active series (default behavior)
        targetSeriesQuery = 'SELECT * FROM invoice_series WHERE company_id = ? AND active = 1 ORDER BY series LIMIT 1';
        targetSeriesParams = [companyId];
      }

      const seriesStmt = this.db.prepare(targetSeriesQuery);
      const targetSeries = await seriesStmt.bind(...targetSeriesParams).first<any>();

      if (!targetSeries) {
        throw new Error(`No ${seriesName ? `series '${seriesName}'` : 'active invoice series'} found for this company.`);
      }

      // Increment the current number atomically
      const newNumber = targetSeries.current_number + 1;
      const updateStmt = this.db.prepare('UPDATE invoice_series SET current_number = ? WHERE id = ?');
      const updateResult = await updateStmt.bind(newNumber, targetSeries.id).run();

      if (!updateResult.success) {
        throw new Error(`Failed to update invoice series: ${updateResult.error}`);
      }

      return {
        series: targetSeries.series,
        number: newNumber,
        formattedNumber: String(newNumber).padStart(4, '0'),
      };
    } catch (error) {
      console.error('Error getting next invoice number:', error);
      throw error;
    }
  }

  async getInvoiceSeriesById(companyId: number, seriesId: number): Promise<InvoiceSeries | { error: string } | null> {
    console.log(`Getting invoice series ${seriesId} for company ${companyId}`);

    try {
      const stmt = this.db.prepare('SELECT * FROM invoice_series WHERE id = ? AND company_id = ?');
      const series = await stmt.bind(seriesId, companyId).first<any>();

      if (!series) {
        return { error: 'Series not found or access denied' };
      }

      return {
        id: series.id,
        company_id: series.company_id,
        series_name: series.series,
        current_number: series.current_number,
        is_default: false
      };
    } catch (error) {
      console.error('Error fetching invoice series by ID:', error);
      return { error: 'Database error occurred' };
    }
  }

  async updateInvoiceSeries(companyId: number, seriesId: number, updateData: Partial<InvoiceSeries>): Promise<InvoiceSeries | { error: string }> {
    console.log(`Updating invoice series ${seriesId} for company ${companyId}`);

    try {
      const existingSeries = await this.getInvoiceSeriesById(companyId, seriesId);
      if (!existingSeries || 'error' in existingSeries) {
        return { error: 'Series not found or access denied' };
      }

      // Build dynamic update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updateData.series_name !== undefined) {
        updateFields.push('series = ?');
        updateValues.push(updateData.series_name);
      }
      if (updateData.current_number !== undefined) {
        updateFields.push('current_number = ?');
        updateValues.push(updateData.current_number);
      }

      if (updateFields.length === 0) {
        return existingSeries; // No updates to perform
      }

      updateValues.push(seriesId, companyId); // For WHERE clause

      const sql = `UPDATE invoice_series SET ${updateFields.join(', ')} WHERE id = ? AND company_id = ?`;
      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...updateValues).run();

      if (!result.success) {
        return { error: `Failed to update series: ${result.error}` };
      }

      // Return updated series
      const updatedSeries = await this.getInvoiceSeriesById(companyId, seriesId);
      return updatedSeries || { error: 'Failed to retrieve updated series' };
    } catch (error) {
      console.error('Error updating invoice series:', error);
      return { error: 'Database error occurred' };
    }
  }

  async deleteInvoiceSeries(companyId: number, seriesId: number): Promise<{ success: boolean } | { error: string }> {
    console.log(`Deleting invoice series ${seriesId} for company ${companyId}`);

    try {
      const existingSeries = await this.getInvoiceSeriesById(companyId, seriesId);
      if (!existingSeries || 'error' in existingSeries) {
        return { error: 'Series not found or access denied' };
      }

      // Check if series has existing invoices
      const countStmt = this.db.prepare('SELECT COUNT(*) as count FROM invoices WHERE series_id = ?');
      const countResult = await countStmt.bind(seriesId).first<{ count: number }>();

      if (countResult && countResult.count > 0) {
        return { error: 'Cannot delete series with existing invoices' };
      }

      // Delete the series
      const deleteStmt = this.db.prepare('DELETE FROM invoice_series WHERE id = ? AND company_id = ?');
      const deleteResult = await deleteStmt.bind(seriesId, companyId).run();

      if (!deleteResult.success) {
        return { error: `Failed to delete series: ${deleteResult.error}` };
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting invoice series:', error);
      return { error: 'Database error occurred' };
    }
  }

  // Methods for managing clients and products could also be part of this service or separate services
  // depending on complexity and preference.
  // For now, keeping them in invoice.service.ts for simplicity of this exercise.
}

// Example instantiation:
// export default {
//   async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
//     const companyService = new CompanyService(env.DB);
//     // ... route requests ...
//   }
// }