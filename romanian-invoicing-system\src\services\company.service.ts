// Service for managing company-specific settings and data
import { D1Database } from '@cloudflare/workers-types';

// Corresponds to the 'companies' table in your schema.sql
export interface Company {
  id?: number;
  name: string;
  cif: string; // VAT ID / CUI
  trade_register_number: string; // Nr. Reg. Com. (J...)
  address: string;
  city: string;
  county: string; // Judet
  postal_code?: string;
  country: string; // e.g., 'RO'
  iban?: string;
  bank_name?: string;
  email?: string;
  phone?: string;
  logo_url?: string;
  // ANAF/eFactura specific settings
  anaf_client_id?: string; // Store securely, ideally in secrets manager
  anaf_client_secret?: string; // Store securely
  anaf_redirect_uri?: string;
  anaf_spv_access_token?: string; // Current access token, store securely
  anaf_spv_refresh_token?: string; // Refresh token, store securely
  anaf_spv_token_expires_at?: string; // ISO Date string
  efactura_socrate_api_key?: string; // If using Socrate, store securely
  // Default settings for invoices
  default_currency?: string; // e.g., 'RON'
  default_vat_rate?: number; // e.g., 19
  // ... other company settings
  created_at?: string;
  updated_at?: string;
}

// For invoice series, corresponds to 'invoice_series' table
export interface InvoiceSeries {
  id?: number;
  company_id: number;
  series_name: string; // e.g., 'ABC', 'XYZ'
  current_number: number;
  is_default?: boolean;
  // Potentially add format for number padding, etc.
}

export class CompanyService {
  private db: D1Database;

  constructor(db: D1Database) {
    this.db = db;
  }

  async createCompany(companyData: Omit<Company, 'id' | 'created_at' | 'updated_at'>): Promise<Company> {
    console.log('Creating company:', companyData.name);
    // TODO: Implement database insertion for a new company
    // const stmt = this.db.prepare('INSERT INTO companies (name, cif, ...) VALUES (?, ?, ...);');
    // const { results } = await stmt.bind(companyData.name, companyData.cif, ...).run();
    // const newCompanyId = results.lastRowId; // This depends on D1's API for returning ID

    // Placeholder implementation
    const newCompanyId = Math.floor(Math.random() * 1000);
    const newCompany: Company = {
      ...companyData,
      id: newCompanyId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    // Simulate DB save: await this.db.prepare("INSERT INTO companies (...)").bind(...).run();
    return newCompany;
  }

  async getCompanyById(companyId: number): Promise<Company | null> {
    console.log(`Getting company by ID: ${companyId}`);
    // TODO: Implement database query to fetch company by ID
    // const stmt = this.db.prepare('SELECT * FROM companies WHERE id = ?');
    // const company = await stmt.bind(companyId).first<Company>();
    // return company;

    // Placeholder
    if (companyId === 1) {
      return {
        id: 1,
        name: 'Example SRL',
        cif: 'RO12345678',
        trade_register_number: 'J40/123/2000',
        address: 'Str. Exemplu, Nr. 10',
        city: 'Bucuresti',
        county: 'Bucuresti',
        country: 'RO',
        email: '<EMAIL>',
        default_currency: 'RON',
        default_vat_rate: 19,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    }
    return null;
  }

  async updateCompany(companyId: number, updateData: Partial<Omit<Company, 'id' | 'created_at' | 'updated_at'>>): Promise<Company | null> {
    console.log(`Updating company ${companyId}:`, updateData);
    // TODO: Implement database update for company settings
    // Ensure sensitive fields like API keys are handled carefully (e.g., only update if provided)
    // const existingCompany = await this.getCompanyById(companyId);
    // if (!existingCompany) return null;
    // const updatedCompany = { ...existingCompany, ...updateData, updated_at: new Date().toISOString() };
    // Build SET clause dynamically for SQL update
    // await this.db.prepare('UPDATE companies SET name = ?, ... WHERE id = ?').bind(..., companyId).run();
    // return updatedCompany;

    // Placeholder
    const existingCompany = await this.getCompanyById(companyId);
    if (!existingCompany) return null;
    const updatedCompany = { ...existingCompany, ...updateData, updated_at: new Date().toISOString() };
    return updatedCompany;
  }

  // --- Invoice Series Management ---

  async createInvoiceSeries(seriesData: Omit<InvoiceSeries, 'id'>): Promise<InvoiceSeries> {
    console.log(`Creating invoice series for company ${seriesData.company_id}: ${seriesData.series_name}`);
    // TODO: DB insert into 'invoice_series'
    // Placeholder
    const newSeriesId = Math.floor(Math.random() * 10000);
    const newSeries: InvoiceSeries = { ...seriesData, id: newSeriesId };
    return newSeries;
  }

  async getInvoiceSeries(companyId: number): Promise<InvoiceSeries[]> {
    console.log(`Getting invoice series for company ${companyId}`);
    // TODO: DB query for all series of a company
    // Placeholder
    if (companyId === 1) {
      return [
        { id: 1, company_id: 1, series_name: 'ABC', current_number: 1000, is_default: true },
        { id: 2, company_id: 1, series_name: 'XYZ', current_number: 500, is_default: false },
      ];
    }
    return [];
  }

  async getNextInvoiceNumber(companyId: number, seriesName?: string): Promise<{ series: string; number: number; formattedNumber: string }> {
    console.log(`Getting next invoice number for company ${companyId}, series ${seriesName || 'default'}`);
    // TODO: Implement logic to get the default or specified series, increment its number (atomically if possible),
    // and return the new number. This is a critical section that needs careful implementation to avoid race conditions.
    // 1. Find the series (default or by name).
    // 2. Lock the series row (if DB supports SELECT ... FOR UPDATE).
    // 3. Read current_number.
    // 4. Increment current_number.
    // 5. Update the series row with the new current_number.
    // 6. Commit transaction.
    // If D1 doesn't support row locking easily, this might need a different strategy or accept minor risks.

    // Placeholder - this is NOT safe for concurrent use
    const seriesList = await this.getInvoiceSeries(companyId);
    let targetSeries = seriesName ? seriesList.find(s => s.series_name === seriesName) : seriesList.find(s => s.is_default);
    if (!targetSeries) {
      if (seriesList.length > 0) targetSeries = seriesList[0];
      else throw new Error('No invoice series configured for this company.');
    }
    targetSeries.current_number += 1;
    // In a real app: await this.db.prepare('UPDATE invoice_series SET current_number = ? WHERE id = ?').bind(targetSeries.current_number, targetSeries.id).run();
    
    return {
      series: targetSeries.series_name,
      number: targetSeries.current_number,
      formattedNumber: String(targetSeries.current_number).padStart(4, '0'), // Example formatting
    };
  }

  async getInvoiceSeriesById(companyId: number, seriesId: number): Promise<InvoiceSeries | { error: string } | null> {
    console.log(`Getting invoice series ${seriesId} for company ${companyId}`);
    // TODO: DB query for specific series
    // Placeholder
    const allSeries = await this.getInvoiceSeries(companyId);
    const series = allSeries.find(s => s.id === seriesId);
    if (!series) {
      return { error: 'Series not found or access denied' };
    }
    return series;
  }

  async updateInvoiceSeries(companyId: number, seriesId: number, updateData: Partial<InvoiceSeries>): Promise<InvoiceSeries | { error: string }> {
    console.log(`Updating invoice series ${seriesId} for company ${companyId}`);
    // TODO: DB update for specific series
    // Placeholder
    const existingSeries = await this.getInvoiceSeriesById(companyId, seriesId);
    if (!existingSeries || 'error' in existingSeries) {
      return { error: 'Series not found or access denied' };
    }

    const updatedSeries: InvoiceSeries = {
      ...existingSeries,
      ...updateData,
      id: seriesId, // Ensure ID doesn't change
      company_id: companyId, // Ensure company_id doesn't change
    };

    // In real implementation: await this.db.prepare('UPDATE invoice_series SET ... WHERE id = ? AND company_id = ?').bind(...).run();
    return updatedSeries;
  }

  async deleteInvoiceSeries(companyId: number, seriesId: number): Promise<{ success: boolean } | { error: string }> {
    console.log(`Deleting invoice series ${seriesId} for company ${companyId}`);
    // TODO: DB delete for specific series
    // Should check if series has existing invoices before deletion
    // Placeholder
    const existingSeries = await this.getInvoiceSeriesById(companyId, seriesId);
    if (!existingSeries || 'error' in existingSeries) {
      return { error: 'Series not found or access denied' };
    }

    // In real implementation:
    // 1. Check if series has invoices: SELECT COUNT(*) FROM invoices WHERE series_id = ?
    // 2. If count > 0, return error: 'Cannot delete series with existing invoices'
    // 3. Otherwise: DELETE FROM invoice_series WHERE id = ? AND company_id = ?

    return { success: true };
  }

  // Methods for managing clients and products could also be part of this service or separate services
  // depending on complexity and preference.
  // For now, keeping them in invoice.service.ts for simplicity of this exercise.
}

// Example instantiation:
// export default {
//   async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
//     const companyService = new CompanyService(env.DB);
//     // ... route requests ...
//   }
// }