// Core service for managing invoices (CRUD operations, business logic)
import { D1Database } from '@cloudflare/workers-types';
import { EFacturaService } from './efactura.service'; // Corrected: Import class and response type
import type { EFacturaUploadResponse, EFacturaStatusResponse } from './efactura.service';
import { AnafService } from './anaf.service'; // Corrected: Import class
import { UblService } from './ubl.service'; // Corrected: Import class

// Define interfaces based on your schema.sql
// These are simplified; you'll need to match your schema.sql precisely.
export interface InvoiceClient {
  id?: number;
  company_id: number;
  name: string;
  cif: string; // Cod Identificare Fiscala (VAT ID)
  // ... other client fields from schema
  email?: string;
  address?: string;
}

export interface InvoiceProduct {
  id?: number;
  company_id: number;
  name: string;
  price: number; // Consider using a library for monetary values (e.g., Dinero.js) if precision is critical
  vat_rate: number; // e.g., 19 for 19%
  // ... other product fields
}

export interface InvoiceItem {
  id?: number;
  invoice_id?: number;
  product_id: number;
  description: string;
  quantity: number;
  unit_price: number;
  vat_rate: number;
  // Calculated fields (value, vat_value, total_value) should ideally be derived or stored carefully
  value?: number; // quantity * unit_price
  vat_value?: number; // value * (vat_rate / 100)
  total_value?: number; // value + vat_value
}

export interface Invoice {
  id?: number;
  company_id: number;
  client_id: number;
  series: string;
  number: string;
  issue_date: string; // ISO 8601 format (YYYY-MM-DD)
  due_date: string;   // ISO 8601 format (YYYY-MM-DD)
  currency: string;   // e.g., RON, EUR
  total_amount: number;
  status: string; // e.g., 'draft', 'issued', 'paid', 'cancelled', 'efactura_pending', 'efactura_sent', 'efactura_error', 'efactura_confirmed'
  efactura_submission_id?: string | null;
  efactura_status?: string | null;
  efactura_error?: string | null;
  notes?: string; // Added for UBL generation
  // Optional fields for UBL generation or more detailed invoices
  subtotal?: number;
  vat_total?: number;
  items?: InvoiceItem[]; // For creating/updating invoices with items
}

export class InvoiceService { // Changed from interface to class
  private db: D1Database;
  private efacturaService: EFacturaService;
  private anafService: AnafService; // Added AnafService
  private ublService: UblService; // Added UblService

  constructor(db: D1Database, efacturaService: EFacturaService, anafService: AnafService, ublService: UblService) {
    this.db = db;
    this.efacturaService = efacturaService;
    this.anafService = anafService; // Initialize AnafService
    this.ublService = ublService; // Initialize UblService
  }

  // Overloaded method signatures to support both old and new calling patterns
  async createInvoice(invoiceData: Omit<Invoice, 'id' | 'items'>, itemsData?: Omit<InvoiceItem, 'id' | 'invoice_id'>[]): Promise<Invoice>;
  async createInvoice(fullInvoiceData: Omit<Invoice, 'id'>): Promise<Invoice>;
  async createInvoice(invoiceDataOrFull: Omit<Invoice, 'id' | 'items'> | Omit<Invoice, 'id'>, itemsData?: Omit<InvoiceItem, 'id' | 'invoice_id'>[]): Promise<Invoice> {
    console.log('Creating invoice:', invoiceDataOrFull);

    // Handle both calling patterns
    let invoiceData: Omit<Invoice, 'id' | 'items'>;
    let items: Omit<InvoiceItem, 'id' | 'invoice_id'>[];

    if ('items' in invoiceDataOrFull && invoiceDataOrFull.items) {
      // Called with full invoice data including items
      const { items: invoiceItems, ...restData } = invoiceDataOrFull as Omit<Invoice, 'id'>;
      invoiceData = restData;
      items = invoiceItems.map(item => {
        const { id, invoice_id, ...itemData } = item;
        return itemData;
      });
    } else {
      // Called with separate invoice data and items
      invoiceData = invoiceDataOrFull as Omit<Invoice, 'id' | 'items'>;
      items = itemsData || [];
    }

    try {
      // Calculate totals from items
      let subtotal = 0;
      let vatTotal = 0;

      const processedItems = items.map(item => {
        const itemSubtotal = item.quantity * item.unit_price;
        const itemVat = itemSubtotal * (item.vat_rate / 100);
        subtotal += itemSubtotal;
        vatTotal += itemVat;

        return {
          ...item,
          subtotal: itemSubtotal,
          vat_amount: itemVat,
          total: itemSubtotal + itemVat
        };
      });

      const totalAmount = subtotal + vatTotal;

      // Insert invoice into database
      const invoiceStmt = this.db.prepare(`
        INSERT INTO invoices (
          company_id, client_id, series_id, number, issue_date, due_date, delivery_date,
          currency, exchange_rate, subtotal, vat_amount, total, payment_method,
          payment_terms, notes, internal_notes, delivery_address, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const invoiceResult = await invoiceStmt.bind(
        invoiceData.company_id,
        invoiceData.client_id,
        1, // series_id - we'll need to get this properly
        invoiceData.number,
        invoiceData.issue_date,
        invoiceData.due_date,
        invoiceData.issue_date, // delivery_date defaults to issue_date
        invoiceData.currency || 'RON',
        1.0, // exchange_rate
        subtotal,
        vatTotal,
        totalAmount,
        null, // payment_method
        null, // payment_terms
        invoiceData.notes || null,
        null, // internal_notes
        null, // delivery_address
        invoiceData.status || 'draft'
      ).run();

      if (!invoiceResult.success) {
        throw new Error(`Failed to create invoice: ${invoiceResult.error}`);
      }

      const newInvoiceId = invoiceResult.meta.last_row_id;
      if (!newInvoiceId) {
        throw new Error('Failed to get new invoice ID');
      }

      // Insert invoice items
      for (const item of processedItems) {
        const itemStmt = this.db.prepare(`
          INSERT INTO invoice_items (
            invoice_id, product_id, name, description, quantity, unit, unit_price,
            discount_percent, discount_amount, subtotal, vat_rate, vat_amount, total
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        await itemStmt.bind(
          newInvoiceId,
          item.product_id || null,
          item.description,
          item.description,
          item.quantity,
          'buc', // default unit
          item.unit_price,
          0, // discount_percent
          0, // discount_amount
          item.subtotal,
          item.vat_rate,
          item.vat_amount,
          item.total
        ).run();
      }

      // Return the created invoice
      return await this.getInvoiceById(Number(newInvoiceId), invoiceData.company_id) as Invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoiceById(invoiceId: number, companyId?: number): Promise<InvoiceWithDetails | null> {
    console.log(`Getting invoice by ID: ${invoiceId}${companyId ? ` for company ${companyId}` : ''}`);

    try {
      // Build query with optional company filter
      let query = `
        SELECT
          i.*,
          c.name as client_name,
          c.cui as client_vat_number,
          c.address as client_address,
          c.city as client_city,
          c.county as client_county,
          c.country as client_country_code,
          s.series as series_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN invoice_series s ON i.series_id = s.id
        WHERE i.id = ?
      `;

      const params = [invoiceId];

      if (companyId) {
        query += ' AND i.company_id = ?';
        params.push(companyId);
      }

      const stmt = this.db.prepare(query);
      const invoice = await stmt.bind(...params).first<any>();

      if (!invoice) {
        return null;
      }

      // Fetch invoice items
      const itemsStmt = this.db.prepare('SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY sort_order, id');
      const { results: items } = await itemsStmt.bind(invoiceId).all<any>();

      // Map database fields to interface
      return {
        id: invoice.id,
        company_id: invoice.company_id,
        client_id: invoice.client_id,
        series: invoice.series_name || '',
        number: invoice.number,
        issue_date: invoice.issue_date,
        due_date: invoice.due_date,
        currency: invoice.currency,
        total_amount: invoice.total,
        status: invoice.status,
        efactura_submission_id: invoice.anaf_upload_index,
        efactura_status: invoice.anaf_status,
        notes: invoice.notes,
        subtotal: invoice.subtotal,
        vat_total: invoice.vat_amount,
        items: items.map((item: any) => ({
          id: item.id,
          invoice_id: item.invoice_id,
          product_id: item.product_id,
          description: item.name,
          quantity: item.quantity,
          unit_price: item.unit_price,
          vat_rate: item.vat_rate,
          value: item.subtotal,
          vat_value: item.vat_amount,
          total_value: item.total
        })),
        client_name: invoice.client_name || '',
        client_vat_number: invoice.client_vat_number || '',
        client_address: invoice.client_address || '',
        client_city: invoice.client_city || '',
        client_county: invoice.client_county || '',
        client_country_code: invoice.client_country_code || 'RO',
        series_name: invoice.series_name || '',
        series_number: invoice.number,
        created_at: invoice.created_at,
        updated_at: invoice.updated_at
      };
    } catch (error) {
      console.error('Error fetching invoice:', error);
      throw error;
    }
  }

  // Overloaded updateInvoice to support different parameter orders
  async updateInvoice(invoiceId: number, updateData: Partial<Invoice>, companyId?: number): Promise<Invoice | null>;
  async updateInvoice(invoiceId: number, companyId: number, updateData: Partial<Invoice>): Promise<Invoice | null>;
  async updateInvoice(invoiceId: number, updateDataOrCompanyId: Partial<Invoice> | number, companyIdOrUpdateData?: number | Partial<Invoice>): Promise<Invoice | null> {
    let updateData: Partial<Invoice>;
    let companyId: number | undefined;

    if (typeof updateDataOrCompanyId === 'number') {
      // Called with (invoiceId, companyId, updateData)
      companyId = updateDataOrCompanyId;
      updateData = companyIdOrUpdateData as Partial<Invoice>;
    } else {
      // Called with (invoiceId, updateData, companyId?)
      updateData = updateDataOrCompanyId;
      companyId = companyIdOrUpdateData as number | undefined;
    }

    console.log(`Updating invoice ${invoiceId}${companyId ? ` for company ${companyId}` : ''}:`, updateData);

    try {
      // Check if invoice exists
      const existingInvoice = await this.getInvoiceById(invoiceId, companyId);
      if (!existingInvoice) return null;

      // Build dynamic update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(updateData.status);
      }
      if (updateData.efactura_status !== undefined) {
        updateFields.push('anaf_status = ?');
        updateValues.push(updateData.efactura_status);
      }
      if (updateData.efactura_submission_id !== undefined) {
        updateFields.push('anaf_upload_index = ?');
        updateValues.push(updateData.efactura_submission_id);
      }
      if (updateData.notes !== undefined) {
        updateFields.push('notes = ?');
        updateValues.push(updateData.notes);
      }
      if (updateData.due_date !== undefined) {
        updateFields.push('due_date = ?');
        updateValues.push(updateData.due_date);
      }
      if (updateData.total_amount !== undefined) {
        updateFields.push('total = ?');
        updateValues.push(updateData.total_amount);
      }

      if (updateFields.length === 0) {
        return existingInvoice as Invoice; // No updates to perform
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = ?');
      updateValues.push(new Date().toISOString());

      // Add WHERE clause parameters
      updateValues.push(invoiceId);
      if (companyId) {
        updateValues.push(companyId);
      }

      const whereClause = companyId ? 'WHERE id = ? AND company_id = ?' : 'WHERE id = ?';
      const sql = `UPDATE invoices SET ${updateFields.join(', ')} ${whereClause}`;

      const stmt = this.db.prepare(sql);
      const result = await stmt.bind(...updateValues).run();

      if (!result.success) {
        throw new Error(`Failed to update invoice: ${result.error}`);
      }

      // Return updated invoice
      return await this.getInvoiceById(invoiceId, companyId) as Invoice;
    } catch (error) {
      console.error('Error updating invoice:', error);
      throw error;
    }
  }

  async getInvoiceWithDetails(invoiceId: number, companyId?: number): Promise<InvoiceWithDetails | null> {
    // This method is the same as getInvoiceById for now, but could be extended
    // to include additional details like company information, etc.
    return this.getInvoiceById(invoiceId, companyId);
  }

  async updateInvoiceEFaturaStatus(invoiceId: number, status: string, submissionId?: string, downloadId?: string): Promise<void> {
    console.log(`Updating eFactura status for invoice ${invoiceId}: ${status}`);

    const updateData: Partial<Invoice> = {
      efactura_status: status,
    };

    if (submissionId) {
      updateData.efactura_submission_id = submissionId;
    }

    // Update the invoice status based on eFactura status
    if (status === 'SUBMITTED') {
      updateData.status = 'efactura_sent';
    } else if (status.includes('ERROR') || status.includes('REJECTED')) {
      updateData.status = 'efactura_error';
    }

    await this.updateInvoice(invoiceId, updateData);
  }

  async generateUblXml(invoiceId: number): Promise<string | null> {
    const invoice = await this.getInvoiceById(invoiceId);
    if (!invoice) {
        console.error(`Invoice with ID ${invoiceId} not found.`);
        return null;
    }

    // Placeholder company data
    const company = {
        id: 1,
        name: 'My Company SRL',
        vat_number: 'RO12345678',
        registration_number: 'J40/123/2023',
        address: 'Str. Exemplu Nr. 1',
        city: 'Bucuresti',
        county: 'Bucuresti',
        postal_code: '010101',
        country_code: 'RO',
        email: '<EMAIL>',
        phone: '**********',
        iban: '************************',
        bank_name: 'My Bank',
        user_id: 'user123'
    };

    // Placeholder client data
    const client = {
        name: invoice.client_name || 'N/A Client',
        vat_number: invoice.client_vat_number || null,
        registration_number: null,
        address: invoice.client_address || 'N/A Address',
        city: invoice.client_city || 'N/A City',
        county: invoice.client_county || null,
        postal_code: null,
        country_code: invoice.client_country_code || 'RO',
        email: null,
        phone: null
    };

    // Fetch invoice items
    const itemsStatement = this.db.prepare('SELECT * FROM invoice_items WHERE invoice_id = ?');
    const itemsResult = await itemsStatement.bind(invoiceId).all<InvoiceItem>();
    const invoiceItems: InvoiceItem[] = itemsResult.results || [];

    const fullInvoiceForUbl = {
        ...invoice,
        items: invoiceItems,
        invoice_number: `${invoice.series_name}/${invoice.series_number}`,
    };

    return this.ublService.generateInvoiceUbl(fullInvoiceForUbl as any, company, client);
  }

  async deleteInvoice(invoiceId: number, companyId: number): Promise<boolean> {
    console.log(`Deleting invoice ${invoiceId} for company ${companyId}`);

    try {
      // Check if invoice exists and belongs to company
      const existingInvoice = await this.getInvoiceById(invoiceId, companyId);
      if (!existingInvoice) {
        return false;
      }

      // Delete invoice items first (foreign key constraint)
      const deleteItemsStmt = this.db.prepare('DELETE FROM invoice_items WHERE invoice_id = ?');
      await deleteItemsStmt.bind(invoiceId).run();

      // Delete the invoice
      const deleteInvoiceStmt = this.db.prepare('DELETE FROM invoices WHERE id = ? AND company_id = ?');
      const result = await deleteInvoiceStmt.bind(invoiceId, companyId).run();

      return result.success && result.meta.changes > 0;
    } catch (error) {
      console.error('Error deleting invoice:', error);
      throw error;
    }
  }

  async listInvoices(companyId: number, filters: any = {}): Promise<Invoice[]> {
    console.log(`Listing invoices for company ${companyId} with filters:`, filters);

    try {
      let query = `
        SELECT
          i.*,
          c.name as client_name,
          s.series as series_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN invoice_series s ON i.series_id = s.id
        WHERE i.company_id = ?
      `;

      const params = [companyId];

      // Add filters
      if (filters.status) {
        query += ' AND i.status = ?';
        params.push(filters.status);
      }
      if (filters.client_id) {
        query += ' AND i.client_id = ?';
        params.push(filters.client_id);
      }
      if (filters.date_from) {
        query += ' AND i.issue_date >= ?';
        params.push(filters.date_from);
      }
      if (filters.date_to) {
        query += ' AND i.issue_date <= ?';
        params.push(filters.date_to);
      }

      query += ' ORDER BY i.issue_date DESC, i.id DESC';

      // Add pagination
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);

        if (filters.offset) {
          query += ' OFFSET ?';
          params.push(filters.offset);
        }
      }

      const stmt = this.db.prepare(query);
      const { results } = await stmt.bind(...params).all<any>();

      return results.map((row: any) => ({
        id: row.id,
        company_id: row.company_id,
        client_id: row.client_id,
        series: row.series_name || '',
        number: row.number,
        issue_date: row.issue_date,
        due_date: row.due_date,
        currency: row.currency,
        total_amount: row.total,
        status: row.status,
        efactura_submission_id: row.anaf_upload_index,
        efactura_status: row.anaf_status,
        notes: row.notes,
        subtotal: row.subtotal,
        vat_total: row.vat_amount
      }));
    } catch (error) {
      console.error('Error listing invoices:', error);
      throw error;
    }
  }

  async submitToEFactura(invoiceId: number, companyId: number): Promise<any> {
      const company = {
          id: companyId,
          name: 'My Company SRL',
          vat_number: 'RO12345678',
          registration_number: 'J40/123/2023',
          address: 'Str. Exemplu Nr. 1',
          city: 'Bucuresti',
          county: 'Bucuresti',
          postal_code: '010101',
          country_code: 'RO',
          email: '<EMAIL>',
          phone: '**********',
          iban: '************************',
          bank_name: 'My Bank',
          user_id: 'user123'
      };

      const cif = company.vat_number;
      const isVatPayer = !!(company.vat_number && company.vat_number.startsWith('RO'));

      const invoice = await this.getInvoiceById(invoiceId, companyId);
      if (!invoice) {
          throw new Error('Invoice not found');
      }
      if (invoice.status === 'efactura_sent' || invoice.status === 'efactura_pending') {
          console.log(`Invoice ${invoiceId} already processed or pending with eFactura.`);
          return invoice;
      }

      const ublXml = await this.generateUblXml(invoiceId);
      if (!ublXml) {
          await this.updateInvoice(invoiceId, { efactura_status: 'UBL Generation Failed', status: 'efactura_error' }, companyId);
          throw new Error('Failed to generate UBL XML for e-Factura submission.');
      }

      try {
          await this.updateInvoice(invoiceId, { status: 'efactura_pending', efactura_status: 'Submitting to eFactura...' }, companyId);
          
          const submissionResult = await this.efacturaService.submitInvoice(ublXml, cif, isVatPayer);

          if ('error' in submissionResult && submissionResult.error) {
              await this.updateInvoice(invoiceId, { efactura_status: `Submission Error: ${submissionResult.error}`, status: 'efactura_error' }, companyId);
              throw new Error(`e-Factura submission failed: ${submissionResult.error}`);
          }

          // Type guard to ensure we're working with SocrateInvoiceResponse
          const uploadResponse = submissionResult as any;

          const updatePayload: Partial<Invoice> = {
              efactura_submission_id: uploadResponse.id || uploadResponse.requestId,
              efactura_status: uploadResponse.messages && uploadResponse.messages.length > 0 ?
                  uploadResponse.messages.map((m: any) => `${m.type}: ${m.details}`).join('; ') :
                  `Status: ${uploadResponse.status}`,
          };

          if (uploadResponse.id && uploadResponse.status !== 'REJECTED') {
              updatePayload.status = 'efactura_sent';
          } else {
              updatePayload.status = 'efactura_error';
          }
          return await this.updateInvoice(invoiceId, updatePayload, companyId);

      } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.updateInvoice(invoiceId, { efactura_status: `Submission Exception: ${errorMessage}`, status: 'efactura_error' }, companyId);
          throw error;
      }
  }

  async checkEFacturaStatus(invoiceId: number, companyId: number): Promise<any> {
      const invoice = await this.getInvoiceById(invoiceId, companyId);
      if (!invoice || !invoice.efactura_submission_id) {
          throw new Error('Invoice not found or not submitted to eFactura yet.');
      }

      try {
          const statusResult = await this.efacturaService.checkInvoiceStatus(invoice.efactura_submission_id);

          // Check if the result is an error
          if ('error' in statusResult) {
              throw new Error(statusResult.error);
          }

          // Now we know statusResult is SocrateStatusResponse
          let newStatus = invoice.status;
          let newEfacturaStatus = `Status: ${statusResult.status}`;

          if (statusResult.anafStatus) {
              newEfacturaStatus += `. ANAF: ${statusResult.anafStatus}`;
          }

          if (statusResult.messages && statusResult.messages.length > 0) {
              newEfacturaStatus += `. Messages: ${statusResult.messages.map(m => `${m.type}: ${m.details}`).join('; ')}`;
          }

          if (statusResult.status === 'SIGNED') {
              newStatus = 'efactura_confirmed';
              newEfacturaStatus = `Successfully processed and signed by ANAF`;
          } else if (statusResult.status === 'REJECTED') {
              newStatus = 'efactura_error';
              newEfacturaStatus = `Rejected by ANAF: ${statusResult.messages?.map(m => m.details).join('; ') || 'Unknown error'}`;
          } else if (statusResult.status === 'UPLOADED') {
              newStatus = 'efactura_pending';
              newEfacturaStatus = 'Uploaded to ANAF, awaiting processing';
          }

          await this.updateInvoice(invoiceId, { status: newStatus, efactura_status: newEfacturaStatus }, companyId);
          return { current_status: newStatus, details: statusResult };

      } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.updateInvoice(invoiceId, { efactura_status: `Status Check Exception: ${errorMessage}` }, companyId);
          throw error;
      }
  }
}

// How you might instantiate and use it in a Cloudflare Worker:
// export default {
//   async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
//     const invoiceService = new InvoiceService(env.DB); // Assuming DB is your D1 binding
//     // ... route requests to service methods ...
//   }
// }


// Placeholder for a more detailed invoice type, perhaps including items, client details, etc.
export interface InvoiceWithDetails extends Invoice {
    client_name: string;
    client_vat_number: string;
    client_address: string;
    client_city: string;
    client_county: string;
    client_country_code: string;
    series_name: string;
    series_number: string;
    efactura_submission_id?: string;
    efactura_status?: string;
    created_at: string;
    updated_at: string;
    // Add company and client objects for compatibility with invoice routes
    company?: {
        id: number;
        name: string;
        cif: string;
        cui: string; // Romanian CUI (same as cif but different property name)
        address: string;
        city: string;
        county: string;
        country: string;
        email?: string;
        phone?: string;
        platitor_tva?: boolean; // VAT payer status
    };
    client?: {
        id: number;
        name: string;
        cif: string;
        address: string;
        city: string;
        county: string;
        country_code: string;
        email?: string;
        phone?: string;
    };
}