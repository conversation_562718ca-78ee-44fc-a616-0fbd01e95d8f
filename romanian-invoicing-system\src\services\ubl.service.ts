// Import types from invoice service instead of non-existent schema
import type { Invoice, InvoiceItem } from './invoice.service';

// Define Company interface for UBL generation
interface Company {
    id: number;
    name: string;
    vat_number?: string;
    cui: string;
    address: string;
    city: string;
    county: string;
    country_code?: string;
    postal_code?: string;
    email?: string;
    phone?: string;
    iban?: string;
    bank_name?: string;
    legal_form?: string;
}

// Define a basic Client type, adjust as per your actual client data structure
interface Client {
    name: string;
    vat_number?: string | null; // Changed from vat to vat_number to match Company schema pattern
    registration_number?: string | null; // Changed from registrationNumber
    address: string;
    city: string;
    county?: string | null;
    postal_code?: string | null; // Changed from postalCode
    country_code: string; // ISO 3166-1 alpha-2 code, e.g., "RO" // Changed from country
    email?: string | null;
    phone?: string | null;
}

export class UblService {
    // Basic XML escaping
    private escapeXml(unsafe: string | number | null | undefined): string {
        if (unsafe === null || unsafe === undefined) return '';
        const text = String(unsafe);
        // Expanded to include control characters as per the previous attempt
        return text.replace(/[<>&'"\u0000-\u001F\u007F-\u009F]/g, (c: string) => {
            switch (c) {
                case '<': return '&lt;';
                case '>': return '&gt;';
                case '&': return '&amp;';
                case '\'': return '&apos;';
                case '"': return '&quot;';
                default: return `&#${c.charCodeAt(0)};`; // Control characters
            }
        });
    }

    private formatDate(date: Date | string | null | undefined): string {
        if (!date) return ''; // Handle null or undefined dates
        const d = new Date(date);
        const year = d.getFullYear();
        const month = ('0' + (d.getMonth() + 1)).slice(-2);
        const day = ('0' + d.getDate()).slice(-2);
        return `${year}-${month}-${day}`;
    }

    public generateInvoiceUbl(invoice: Invoice, company: Company, client: Client): string {
        const ublVersion = '2.1';
        const customizationId = 'urn:cen.eu:en16931:2017#compliant#urn:efactura.mfinante.ro:CIUS-RO:1.0.1';
        const profileId = 'urn:cen.eu:en16931:2017';

        const issueDate = this.formatDate(invoice.issue_date);
        const dueDate = this.formatDate(invoice.due_date);

        // Calculate totals from items
        let totalNetAmount = 0;
        let totalVatAmount = 0;
        let totalGrossAmount = 0;

        (invoice.items as InvoiceItem[]).forEach(item => {
            const itemNetAmount = item.quantity * item.unit_price;
            const itemVatAmount = itemNetAmount * (item.vat_rate / 100);
            totalNetAmount += itemNetAmount;
            totalVatAmount += itemVatAmount;
            totalGrossAmount += (itemNetAmount + itemVatAmount);
        });

        // Fallback to invoice totals if items are not detailed or sums differ (e.g. due to rounding)
        // It's better if invoice totals are derived from items consistently
        const finalTotalNetAmount = (invoice.subtotal || totalNetAmount); // Use subtotal or calculated from items
        const finalTotalVatAmount = (invoice.vat_total || totalVatAmount); // Use vat_total or calculated from items
        const finalTotalGrossAmount = invoice.total_amount; // Use total_amount from invoice


        let ubl = `<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
    <cbc:UBLVersionID>${ublVersion}</cbc:UBLVersionID>
    <cbc:CustomizationID>${customizationId}</cbc:CustomizationID>
    <cbc:ProfileID>${profileId}</cbc:ProfileID>
    <cbc:ID>${this.escapeXml(invoice.series)}${this.escapeXml(invoice.number)}</cbc:ID>
    <cbc:IssueDate>${issueDate}</cbc:IssueDate>
    <cbc:DueDate>${dueDate}</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode> <!-- 380 for Commercial invoice -->
    <cbc:Note>${this.escapeXml(invoice.notes || 'Factura emisa conform legislatiei in vigoare.')}</cbc:Note>
    <cbc:DocumentCurrencyCode>${this.escapeXml(invoice.currency)}</cbc:DocumentCurrencyCode>
    <cbc:TaxCurrencyCode>${this.escapeXml(invoice.currency)}</cbc:TaxCurrencyCode> <!-- Usually same as document currency -->
    <cbc:BuyerReference>${this.escapeXml(client.name)}</cbc:BuyerReference> <!-- Or a contract number if available -->

    <!-- AccountingSupplierParty (Your Company) -->
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cac:PartyName>
                <cbc:Name>${this.escapeXml(company.name)}</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>${this.escapeXml(company.address)}</cbc:StreetName>
                <cbc:CityName>${this.escapeXml(company.city)}</cbc:CityName>
                ${company.postal_code ? `<cbc:PostalZone>${this.escapeXml(company.postal_code)}</cbc:PostalZone>` : ''}
                <cbc:CountrySubentity>${this.escapeXml(company.county)}</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>${this.escapeXml(company.country_code || 'RO')}</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>${this.escapeXml(company.vat_number || company.cui)}</cbc:CompanyID> <!-- Use VAT if available, else CUI -->
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>${this.escapeXml(company.name)}</cbc:RegistrationName>
                <cbc:CompanyID schemeID="ONRC">${this.escapeXml(company.cui)}</cbc:CompanyID> <!-- Assuming CUI is the Trade Register Number for this context -->
                ${company.legal_form ? `<cbc:CompanyLegalForm>${this.escapeXml(company.legal_form)}</cbc:CompanyLegalForm>` : ''}
            </cac:PartyLegalEntity>
            <cac:Contact>
                ${company.phone ? `<cbc:Telephone>${this.escapeXml(company.phone)}</cbc:Telephone>` : ''}
                ${company.email ? `<cbc:ElectronicMail>${this.escapeXml(company.email)}</cbc:ElectronicMail>` : ''}
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>

    <!-- AccountingCustomerParty (Client) -->
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cac:PartyName>
                <cbc:Name>${this.escapeXml(client.name)}</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>${this.escapeXml(client.address)}</cbc:StreetName>
                <cbc:CityName>${this.escapeXml(client.city)}</cbc:CityName>
                ${client.postal_code ? `<cbc:PostalZone>${this.escapeXml(client.postal_code)}</cbc:PostalZone>` : ''}
                ${client.county ? `<cbc:CountrySubentity>${this.escapeXml(client.county)}</cbc:CountrySubentity>` : ''}
                <cac:Country>
                    <cbc:IdentificationCode>${this.escapeXml(client.country_code)}</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            ${client.vat_number ? 
            `<cac:PartyTaxScheme>
                <cbc:CompanyID>${this.escapeXml(client.vat_number)}</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>` : ''}
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>${this.escapeXml(client.name)}</cbc:RegistrationName>
                ${client.registration_number ? `<cbc:CompanyID schemeID="ONRC">${this.escapeXml(client.registration_number)}</cbc:CompanyID>` : ''}
            </cac:PartyLegalEntity>
            <cac:Contact>
                 ${client.phone ? `<cbc:Telephone>${this.escapeXml(client.phone)}</cbc:Telephone>` : ''}
                 ${client.email ? `<cbc:ElectronicMail>${this.escapeXml(client.email)}</cbc:ElectronicMail>` : ''}
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>

    <!-- PaymentMeans -->
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode>30</cbc:PaymentMeansCode> <!-- 30 for Credit Transfer, adjust as needed -->
        <cbc:PaymentDueDate>${dueDate}</cbc:PaymentDueDate>
        ${company.iban && company.bank_name ? 
        `<cac:PayeeFinancialAccount>
            <cbc:ID>${this.escapeXml(company.iban)}</cbc:ID>
            <cac:FinancialInstitutionBranch>
                <cac:FinancialInstitution>
                    <cbc:Name>${this.escapeXml(company.bank_name)}</cbc:Name>
                </cac:FinancialInstitution>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>` : ''}
    </cac:PaymentMeans>

    <!-- TaxTotal -->
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalVatAmount.toFixed(2)}</cbc:TaxAmount>
        <!-- Add TaxSubtotal for each VAT rate if needed. This is a simplified version. -->
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalNetAmount.toFixed(2)}</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalVatAmount.toFixed(2)}</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID> <!-- Standard rate. For multiple rates, repeat TaxSubtotal. -->
                <cbc:Percent>${invoice.items && (invoice.items as InvoiceItem[]).length > 0 ? (invoice.items[0] as InvoiceItem).vat_rate.toFixed(2) : '19.00'}</cbc:Percent> <!-- Simplification: uses first item's VAT rate. Real scenario needs to handle multiple rates. -->
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>

    <!-- LegalMonetaryTotal -->
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalNetAmount.toFixed(2)}</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalNetAmount.toFixed(2)}</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalGrossAmount.toFixed(2)}</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID="${this.escapeXml(invoice.currency)}">0.00</cbc:AllowanceTotalAmount> <!-- If discounts are applied -->
        <cbc:ChargeTotalAmount currencyID="${this.escapeXml(invoice.currency)}">0.00</cbc:ChargeTotalAmount> <!-- If charges are applied -->
        <cbc:PrepaidAmount currencyID="${this.escapeXml(invoice.currency)}">0.00</cbc:PrepaidAmount>
        <cbc:PayableRoundingAmount currencyID="${this.escapeXml(invoice.currency)}">0.00</cbc:PayableRoundingAmount>
        <cbc:PayableAmount currencyID="${this.escapeXml(invoice.currency)}">${finalTotalGrossAmount.toFixed(2)}</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
`;

        (invoice.items as InvoiceItem[]).forEach((item, index) => {
            const lineNetAmount = item.quantity * item.unit_price;
            // const lineVatAmount = lineNetAmount * (item.vat_rate / 100);
            ubl += `
    <!-- InvoiceLine -->
    <cac:InvoiceLine>
        <cbc:ID>${index + 1}</cbc:ID>
        <cbc:InvoicedQuantity unitCode="${this.escapeXml(item.unit_measure || 'H87')}">${item.quantity.toFixed(2)}</cbc:InvoicedQuantity> <!-- H87 for piece, C62 for one. Check UN/ECE rec 20. -->
        <cbc:LineExtensionAmount currencyID="${this.escapeXml(invoice.currency)}">${lineNetAmount.toFixed(2)}</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>${this.escapeXml(item.description || item.name)}</cbc:Description>
            <cbc:Name>${this.escapeXml(item.name)}</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID> <!-- Standard rate. Adjust if other rates apply (e.g., K for VAT exempt) -->
                <cbc:Percent>${item.vat_rate.toFixed(2)}</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="${this.escapeXml(invoice.currency)}">${item.unit_price.toFixed(2)}</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>`;
        });

        ubl += `
</Invoice>`;
        return ubl;
    }

  /**
   * Parses a UBL 2.1 Invoice XML string (e.g., received from ANAF SPV).
   * This is a placeholder. A real implementation would use an XML parser and map fields to your internal models.
   */
  parseInvoiceUbl(xmlString: string): Partial<Invoice> {
    console.log('Parsing UBL XML string...');
    // TODO: Implement robust XML parsing
    // This would involve using an XML parser, navigating the UBL structure, and extracting relevant data.
    // Example (very simplified, assumes direct text content):
    const idMatch = xmlString.match(/<cbc:ID>(.*?)<\/cbc:ID>/);
    const issueDateMatch = xmlString.match(/<cbc:IssueDate>(.*?)<\/cbc:IssueDate>/);
    const totalAmountMatch = xmlString.match(/<cbc:PayableAmount.*?>(.*?)<\/cbc:PayableAmount>/);

    const parsedInvoice: Partial<Invoice> = {};
    if (idMatch && idMatch[1]) {
      const parts = idMatch[1].split('-');
      parsedInvoice.series = parts.slice(0, -1).join('-');
      parsedInvoice.number = parts.pop() || '';
    }
    if (issueDateMatch && issueDateMatch[1]) parsedInvoice.issue_date = issueDateMatch[1];
    if (totalAmountMatch && totalAmountMatch[1]) parsedInvoice.total_amount = parseFloat(totalAmountMatch[1]);

    // ... and so on for all other fields and items.
    // This is non-trivial due to namespaces, complex types, and cardinality.

    console.warn('UBL parsing is a placeholder and only extracts minimal data.');
    return parsedInvoice;
  }
}

export const ublService = new UblService();