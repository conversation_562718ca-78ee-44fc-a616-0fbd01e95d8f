{"name": "handmadein-backend", "version": "1.0.0", "description": "Cloudflare Workers backend for Hanmade in RO", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "build": "tsc", "test": "vitest", "test:watch": "vitest --watch", "db:migrate": "wrangler d1 migrations apply handmadein-db", "db:migrate:local": "wrangler d1 migrations apply handmadein-db --local", "db:seed": "wrangler d1 execute handmadein-db --file=./database/seed.sql", "db:seed:local": "wrangler d1 execute handmadein-db --local --file=./database/seed.sql", "db:seed:admin": "wrangler d1 execute handmadein-db --file=./database/seed-admin-user.sql", "db:seed:admin:local": "wrangler d1 execute handmadein-db --local --file=./database/seed-admin-user.sql", "db:generate": "drizzle-kit generate:sqlite", "db:studio": "drizzle-kit studio", "type-check": "tsc --noEmit", "transfer": "node transfer-data.js", "migrate:reviews": "node scripts/migrate-reviews-to-kv.js", "upload:reviews": "node scripts/bulk-upload-reviews-wrangler.js", "upload:reviews:api": "node scripts/bulk-upload-reviews-to-kv.js"}, "dependencies": {"@cloudflare/workers-types": "^4.20240620.0", "@cretezy/cloudflare-d1-backup": "^0.2.1", "@tsndr/cloudflare-worker-jwt": "^2.5.4", "bcryptjs": "^2.4.3", "better-sqlite3": "^11.10.0", "date-fns": "^3.0.0", "drizzle-orm": "^0.30.0", "handmadein-shared": "workspace:*", "hono": "^4.0.0", "stripe": "^14.0.0", "uuid": "^9.0.1", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.7", "csv-parser": "^3.0.0", "drizzle-kit": "^0.20.0", "node-fetch": "^2.7.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "wrangler": "^3.57.0"}, "engines": {"node": ">=18.0.0"}}