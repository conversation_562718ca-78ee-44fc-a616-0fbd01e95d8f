import { Hono } from 'hono';
import { z } from 'zod';
import { validator } from 'hono/validator';
import type { AppEnv, User } from '../index';
import { AuthService } from '../services/auth.service';

const authRoutes = new Hono<AppEnv>();

// Zod validation schemas
const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6), // Adjust password complexity as needed
});

const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8), // Example: require longer password for registration
  // Add other registration fields like name, company_name, etc.
  company_name: z.string().min(1).optional(), // Example: if creating a company on registration
});

// Login endpoint
authRoutes.post('/login', validator('json', (value, c) => {
  const parsed = LoginSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const { email, password } = c.req.valid('json');
  const authService = c.get('authService');

  try {
    // In a real app, authService.loginUser would check credentials against a database
    // For this example, it might be a placeholder or use a simple check
    const user = await authService.loginUser({ email, password });

    if (!user || 'error' in user) {
      return c.json({ error: (user as any)?.error || 'Invalid email or password' }, 401);
    }

    // If login is successful, generate a JWT token
    const token = await authService.generateToken(user, '1h'); // Token expires in 1 hour

    return c.json({ 
      message: 'Login successful',
      token,
      user: { id: user.id, email: user.email /* include other non-sensitive user details */ }
    });
  } catch (error: any) {
    console.error('Login error:', error);
    return c.json({ error: 'Login failed', details: error.message }, 500);
  }
});

// Registration endpoint
authRoutes.post('/register', validator('json', (value, c) => {
  const parsed = RegisterSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const registrationData = c.req.valid('json');
  const authService = c.get('authService');
  const companyService = c.get('companyService'); // For creating a company if needed

  try {
    // TODO: Implement actual user and potentially company creation logic
    // This might involve checking if user/email already exists
    // Hashing password before storing, etc.

    // Placeholder: Attempt to register user
    const newUser = await authService.registerUser(registrationData);
    if ('error' in newUser) {
      return c.json(newUser, 400);
    }

    // Placeholder: If company_name is provided, create a company and associate
    let company = null;
    if (registrationData.company_name) {
        // This is a simplified flow. In reality, you'd gather more company details.
        const companyData = {
            name: registrationData.company_name,
            // ... other default company fields
            owner_user_id: newUser.id // Link company to the new user
        };
        const newCompany = await companyService.createCompany(companyData as any);
        if ('error' in newCompany) {
            // Potentially rollback user creation or handle error appropriately
            console.warn('User created, but company creation failed:', newCompany.error);
            // For now, proceed without company or return specific error
        } else {
            company = newCompany;
            // TODO: Potentially update user with company_id if your schema requires it
        }
    }

    // Generate a token for the newly registered user
    const token = await authService.generateToken(newUser, '1h');

    return c.json({
      message: 'Registration successful',
      token,
      user: { id: newUser.id, email: newUser.email },
      company: company ? { id: company.id, name: company.name } : null
    }, 201);

  } catch (error: any) {
    console.error('Registration error:', error);
    // Check for specific errors, e.g., duplicate email
    if (error.message?.includes('UNIQUE constraint failed: users.email')) {
        return c.json({ error: 'Email already registered' }, 409);
    }
    return c.json({ error: 'Registration failed', details: error.message }, 500);
  }
});

// (Optional) Endpoint to verify token / get current user (useful for frontend)
authRoutes.get('/me', async (c) => {
    const user = c.get('user'); // This should be populated by the authMiddleware
    if (!user) {
        return c.json({ error: 'Not authenticated' }, 401);
    }
    // Return non-sensitive user information
    return c.json({ id: user.id, email: user.email, company_id: user.company_id });
});

export default authRoutes;