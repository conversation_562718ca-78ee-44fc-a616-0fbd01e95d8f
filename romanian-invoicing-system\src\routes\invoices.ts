import { Hono } from 'hono';
import { z } from 'zod';
import { validator } from 'hono/validator';
import { createMiddleware } from 'hono/factory';
import type { Env, AppEnv } from '../index'; // Assuming Env and AppContext are in index.ts
// import { authMiddleware } from '../middleware/auth'; // Will be applied in index.ts
import { InvoiceService, type InvoiceWithDetails } from '../services/invoice.service'; // Assuming an exported type for detailed invoice
import { EFacturaService } from '../services/efactura.service';
import { PdfService } from '../services/pdf.service';
import { StorageService } from '../services/storage.service';
import { UblService } from '../services/ubl.service';
import { AnafService } from '../services/anaf.service';
import { CompanyService } from '../services/company.service';

const invoiceRoutes = new Hono<AppEnv>();

// Zod validation schemas
const CreateInvoiceItemSchema = z.object({
  product_id: z.number().int().positive().optional(),
  name: z.string().min(1, "Item name cannot be empty.").max(255),
  quantity: z.number().positive("Quantity must be positive."),
  unit_price: z.number().nonnegative("Unit price cannot be negative."),
  vat_rate: z.number().min(0).max(100, "VAT rate must be between 0 and 100."),
});

const CreateInvoiceSchema = z.object({
  client_id: z.number().int().positive(),
  series_id: z.number().int().positive(),
  issue_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Issue date must be in YYYY-MM-DD format."),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Due date must be in YYYY-MM-DD format.").optional(),
  currency: z.string().length(3).default('RON'),
  notes: z.string().max(1000).optional(),
  items: z.array(CreateInvoiceItemSchema).min(1, "An invoice must have at least one item."),
});

// Infer TS types from Zod schemas for better type safety
type CreateInvoicePayload = z.infer<typeof CreateInvoiceSchema>;
type UpdateInvoicePayload = z.infer<typeof UpdateInvoiceSchema>;

const UpdateInvoiceSchema = CreateInvoiceSchema.partial().extend({
  status: z.string().optional() // Allow status updates
});


// --- Invoice CRUD Endpoints ---

// Create a new invoice
invoiceRoutes.post('/', validator('json', (value, c) => {
  const parsed = CreateInvoiceSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const invoiceData = c.req.valid('json');
  const invoiceService = c.get('invoiceService');
  const companyService = c.get('companyService');
  const user = c.get('user');

  if (!user || !user.company_id) {
      return c.json({ error: 'User or company information not found' }, 401);
  }

  try {
    // Get next invoice number for the specified series
    const nextInvoiceNumberResult = await companyService.getNextInvoiceNumber(user.company_id, String(invoiceData.series_id));
    if ('error' in nextInvoiceNumberResult) {
        return c.json({ error: nextInvoiceNumberResult.error }, 400);
    }

    // Calculate total amount from items
    const totalAmount = invoiceData.items.reduce((sum, item) => {
        return sum + (item.quantity * item.unit_price * (1 + item.vat_rate / 100));
    }, 0);

    // Combine validated data with server-generated data
    const fullInvoiceData = {
        ...invoiceData,
        company_id: user.company_id, // Enforce company_id from authenticated user
        series: nextInvoiceNumberResult.series || 'INV', // Use series from result or default
        number: String(nextInvoiceNumberResult.number),
        due_date: invoiceData.due_date || invoiceData.issue_date, // Default due_date to issue_date if not provided
        total_amount: totalAmount,
        status: 'draft' as const, // All new invoices start as drafts
    };

    const newInvoice = await invoiceService.createInvoice(fullInvoiceData);
    if ('error' in newInvoice) {
      return c.json(newInvoice, 400);
    }
    return c.json(newInvoice, 201);
  } catch (error: any) {
    console.error('Error creating invoice:', error);
    return c.json({ error: 'Failed to create invoice', details: error.message }, 500);
  }
});

// Get an invoice by ID
invoiceRoutes.get('/:id{[0-9]+}', async (c) => {
  const id = parseInt(c.req.param('id'));
  const invoiceService = c.get('invoiceService');
  const user = c.get('user');

  if (!user || !user.company_id) {
      return c.json({ error: 'Authentication required' }, 401);
  }

  try {
    // The service should handle the logic of joining related tables (client, items, etc.)
    const invoice = await invoiceService.getInvoiceById(id, user.company_id);
    if (!invoice || ('error' in invoice && invoice.error)) {
      return c.json({ error: 'Invoice not found or access denied' }, 404);
    }
    return c.json(invoice);
  } catch (error: any) {
    console.error(`Error fetching invoice ${id}:`, error);
    return c.json({ error: 'Failed to fetch invoice', details: error.message }, 500);
  }
});

// List invoices (with pagination and filtering)
invoiceRoutes.get('/', async (c) => {
  const invoiceService = c.get('invoiceService');
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'Authentication required' }, 401);
  }

  try {
    const { limit = '20', offset = '0', status, client_id } = c.req.query();
    const filterOptions = {
        limit: parseInt(limit, 10),
        offset: parseInt(offset, 10),
        status,
        client_id: client_id ? parseInt(client_id, 10) : undefined,
    };

    if (isNaN(filterOptions.limit) || isNaN(filterOptions.offset) || (filterOptions.client_id !== undefined && isNaN(filterOptions.client_id))) {
      return c.json({ error: 'Invalid pagination or filter parameters' }, 400);
    }

    const invoices = await invoiceService.listInvoices(user.company_id, filterOptions);
    if ('error' in invoices) {
        return c.json(invoices, 400);
    }
    return c.json(invoices);
  } catch (error: any) {
    console.error('Error listing invoices:', error);
    return c.json({ error: 'Failed to list invoices', details: error.message }, 500);
  }
});

// Update an invoice
invoiceRoutes.put('/:id{[0-9]+}', validator('json', (value, c) => {
    const parsed = UpdateInvoiceSchema.safeParse(value);
    if (!parsed.success) {
        return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
    }
    return parsed.data;
}), async (c) => {
    const id = parseInt(c.req.param('id'));
    const invoiceData = c.req.valid('json');
    const invoiceService = c.get('invoiceService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    try {
        const updatedInvoice = await invoiceService.updateInvoice(id, user.company_id, invoiceData as any);
        if (!updatedInvoice || 'error' in updatedInvoice) {
            const statusCode = updatedInvoice?.error === 'Invoice not found or access denied' ? 404 : 400;
            return c.json(updatedInvoice || { error: 'Failed to update invoice' }, statusCode);
        }
        return c.json(updatedInvoice);
    } catch (error: any) {
        console.error(`Error updating invoice ${id}:`, error);
        return c.json({ error: 'Failed to update invoice', details: error.message }, 500);
    }
});

// Delete an invoice (can be soft or hard delete, handled by the service)
invoiceRoutes.delete('/:id{[0-9]+}', async (c) => {
    const id = parseInt(c.req.param('id'));
    const invoiceService = c.get('invoiceService');
    const user = c.get('user');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    try {
        const result = await invoiceService.deleteInvoice(id, user.company_id);
        if (!result) {
            return c.json({ error: 'Invoice not found or access denied' }, 404);
        }
        return c.json({ message: 'Invoice deleted successfully' });
    } catch (error: any) {
        console.error(`Error deleting invoice ${id}:`, error);
        return c.json({ error: 'Failed to delete invoice', details: error.message }, 500);
    }
});


// --- E-Factura Middleware & Endpoints ---

// Middleware to initialize a request-specific EFacturaService based on the user's company settings.
const eFacturaServiceInitializer = createMiddleware<AppEnv>(async (c, next) => {
    const user = c.get('user');
    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required for eFactura operation' }, 401);
    }

    const companyService = c.get('companyService');
    const ublService = c.get('ublService');
    const env = c.env;

    const company = await companyService.getCompanyById(user.company_id);
    if (!company || 'error' in company) {
        return c.json({ error: 'Company details not found for eFactura configuration.' }, 404);
    }

    const anafClientId = company.anaf_client_id || env.ANAF_CLIENT_ID_GLOBAL;
    const anafClientSecret = company.anaf_client_secret || env.ANAF_CLIENT_SECRET_GLOBAL;
    const anafTestMode = env.ANAF_TEST_MODE === 'true'; // Use global test mode setting

    if (!anafClientId || !anafClientSecret) {
        return c.json({ error: 'ANAF client ID or secret not configured for the company or globally.' }, 400);
    }
    
    // Create a new AnafService instance with company-specific credentials for this request.
    const anafSvcInstance = new AnafService(env.DB, user.company_id, anafClientId, anafClientSecret, anafTestMode);
    const eFacturaService = new EFacturaService(env.DB, anafSvcInstance, ublService, user.company_id, anafTestMode);

    c.set('reqEFacturaService', eFacturaService); // Set the request-scoped service in context.
    await next();
});

// Apply the middleware to all eFactura-related routes.
invoiceRoutes.use('/:id/efactura/*', eFacturaServiceInitializer);

// Submit an invoice to ANAF e-Factura
invoiceRoutes.post('/:id{[0-9]+}/efactura/submit', async (c) => {
    const id = parseInt(c.req.param('id'));
    const user = c.get('user'); // Already validated by middleware
    const eFacturaService = c.get('reqEFacturaService');
    const invoiceService = c.get('invoiceService');
    const ublService = c.get('ublService');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    if (!eFacturaService) {
        return c.json({ error: 'eFactura service not initialized' }, 500);
    }

    // It's efficient for the service to fetch the invoice with all its related data (company, client, items) in one go.
    const invoice = await invoiceService.getInvoiceWithDetails(id, user.company_id) as InvoiceWithDetails;
    if (!invoice || 'error' in invoice) {
        return c.json({ error: 'Invoice not found or access denied' }, 404);
    }
    if (!invoice.company || !invoice.client || !invoice.items) {
        return c.json({ error: 'Invoice is missing company, client, or item details required for UBL generation.' }, 500);
    }

    const ublXml = ublService.generateInvoiceUbl(invoice, invoice.company, invoice.client);
    if (!ublXml) {
        return c.json({ error: 'Failed to generate UBL XML for eFactura' }, 500);
    }

    const submissionResult = await eFacturaService.submitInvoice(ublXml, invoice.company.cui, invoice.company.platitor_tva || false);
    if ('error' in submissionResult) {
        return c.json({ error: 'eFactura submission failed', details: submissionResult.error }, 500);
    }

    await invoiceService.updateInvoiceEFaturaStatus(id, 'SUBMITTED', submissionResult.index_incarcare);

    return c.json({ 
        message: 'Invoice submitted to eFactura successfully', 
        submissionId: submissionResult.index_incarcare,
    });
});

// Check the status of a submitted e-Factura
invoiceRoutes.get('/:id{[0-9]+}/efactura/status', async (c) => {
    const id = parseInt(c.req.param('id'));
    const user = c.get('user'); // Already validated
    const eFacturaService = c.get('reqEFacturaService');
    const invoiceService = c.get('invoiceService');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    if (!eFacturaService) {
        return c.json({ error: 'eFactura service not initialized' }, 500);
    }

    const invoice = await invoiceService.getInvoiceById(id, user.company_id);
    if (!invoice || 'error' in invoice || !invoice.efactura_submission_id) {
        return c.json({ error: 'Invoice not found, not submitted to eFactura, or access denied' }, 404);
    }

    const statusResult = await eFacturaService.checkInvoiceStatus(invoice.efactura_submission_id);
    if ('error' in statusResult) {
        return c.json({ error: 'Failed to check eFactura status', details: statusResult.error }, 500);
    }

    // Update the invoice's eFactura status in the database
    await invoiceService.updateInvoiceEFaturaStatus(id, statusResult.stare, invoice.efactura_submission_id, statusResult.id_descarcare_raspuns);

    return c.json(statusResult);
});


// --- PDF Generation Endpoint ---

invoiceRoutes.get('/:id{[0-9]+}/pdf', async (c) => {
    const id = parseInt(c.req.param('id'));
    const invoiceService = c.get('invoiceService');
    const pdfService = c.get('pdfService');
    const storageService = c.get('storageService');
    const user = c.get('user');
    const env = c.env;

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    try {
        // Fetch the invoice with all necessary details for the PDF.
        const invoice = await invoiceService.getInvoiceWithDetails(id, user.company_id) as InvoiceWithDetails;
        if (!invoice || 'error' in invoice) {
            return c.json({ error: 'Invoice not found or access denied' }, 404);
        }
        if (!invoice.company || !invoice.client || !invoice.items) {
            return c.json({ error: 'Client or item details missing for PDF generation' }, 400);
        }

        const pdfBuffer = await pdfService.generateInvoicePdf(invoice, invoice.company, invoice.client, invoice.items, env);
        
        // Store the generated PDF in R2 storage for caching and later retrieval.
        const pdfFileName = `invoices/${user.company_id}/${invoice.series_name || 'INV'}-${invoice.number}.pdf`;
        await storageService.uploadFile(pdfFileName, pdfBuffer, 'application/pdf');

        c.header('Content-Type', 'application/pdf');
        c.header('Content-Disposition', `inline; filename="Factura-${invoice.series_name || 'INV'}-${invoice.number}.pdf"`);
        return c.body(pdfBuffer);

    } catch (error: any) {
        console.error(`Error generating PDF for invoice ${id}:`, error);
        return c.json({ error: 'Failed to generate PDF', details: error.message }, 500);
    }
});

export default invoiceRoutes;