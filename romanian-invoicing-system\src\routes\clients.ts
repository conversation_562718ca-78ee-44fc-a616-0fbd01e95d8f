import { Hono } from 'hono';
import { z } from 'zod';
import { validator } from 'hono/validator';
import type { AppEnv } from '../index'; // Changed AppContext to AppEnv
// Assuming InvoiceService might handle basic client operations or a dedicated ClientService would exist
// For this example, we'll interact with DB via CompanyService context or directly for simplicity
// but a dedicated ClientService is recommended for larger apps.

const clientRoutes = new Hono<AppEnv>();

// Zod validation schema for Client
const ClientSchema = z.object({
  name: z.string().min(1).max(255),
  cif: z.string().max(20).optional(), // Fiscal Code (optional for individuals)
  reg_com: z.string().max(30).optional(), // Trade Registry (optional)
  address: z.string().max(255).optional(),
  city: z.string().max(100).optional(),
  county: z.string().max(100).optional(),
  country: z.string().max(50).default('Romania'),
  bank_name: z.string().max(100).optional(),
  iban: z.string().max(34).optional(),
  email: z.string().email().optional(),
  phone: z.string().max(20).optional(),
  contact_person: z.string().max(100).optional(),
  is_active: z.boolean().default(true),
  // company_id will be derived from the authenticated user's company
});

const UpdateClientSchema = ClientSchema.partial();

// Create a new client
clientRoutes.post('/', validator('json', (value, c) => {
  const parsed = ClientSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const clientData = c.req.valid('json');
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const result = await db.prepare(
      'INSERT INTO clients (company_id, name, cif, reg_com, address, city, county, country, bank_name, iban, email, phone, contact_person, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'
    ).bind(
      user.company_id,
      clientData.name,
      clientData.cif,
      clientData.reg_com,
      clientData.address,
      clientData.city,
      clientData.county,
      clientData.country,
      clientData.bank_name,
      clientData.iban,
      clientData.email,
      clientData.phone,
      clientData.contact_person,
      clientData.is_active
    ).run();

    if (!result.success || result.meta.last_row_id === undefined) {
        return c.json({ error: 'Failed to create client', details: result.error || 'No last_row_id' }, 500);
    }
    
    const newClient = await db.prepare('SELECT * FROM clients WHERE id = ? AND company_id = ?')
                            .bind(result.meta.last_row_id, user.company_id)
                            .first();    

    return c.json(newClient, 201);
  } catch (error: any) {
    console.error('Error creating client:', error);
    return c.json({ error: 'Failed to create client', details: error.message }, 500);
  }
});

// Get all clients for the company
clientRoutes.get('/', async (c) => {
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const { results } = await db.prepare('SELECT * FROM clients WHERE company_id = ? ORDER BY name ASC')
                                .bind(user.company_id)
                                .all();
    return c.json(results);
  } catch (error: any) {
    console.error('Error fetching clients:', error);
    return c.json({ error: 'Failed to fetch clients', details: error.message }, 500);
  }
});

// Get a specific client by ID
clientRoutes.get('/:clientId', async (c) => {
  const clientId = parseInt(c.req.param('clientId'));
  if (isNaN(clientId)) {
    return c.json({ error: 'Invalid client ID' }, 400);
  }
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    const client = await db.prepare('SELECT * FROM clients WHERE id = ? AND company_id = ?')
                            .bind(clientId, user.company_id)
                            .first();
    if (!client) {
      return c.json({ error: 'Client not found or access denied' }, 404);
    }
    return c.json(client);
  } catch (error: any) {
    console.error(`Error fetching client ${clientId}:`, error);
    return c.json({ error: 'Failed to fetch client', details: error.message }, 500);
  }
});

// Update a client
clientRoutes.put('/:clientId', validator('json', (value, c) => {
  const parsed = UpdateClientSchema.safeParse(value);
  if (!parsed.success) {
    return c.json({ error: 'Validation failed', details: parsed.error.flatten() }, 400);
  }
  return parsed.data;
}), async (c) => {
  const clientId = parseInt(c.req.param('clientId'));
  if (isNaN(clientId)) {
    return c.json({ error: 'Invalid client ID' }, 400);
  }
  const clientData = c.req.valid('json');
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  // Construct SET clause dynamically based on provided fields
  const fieldsToUpdate = Object.keys(clientData).filter(key => clientData[key as keyof typeof clientData] !== undefined);
  if (fieldsToUpdate.length === 0) {
    return c.json({ error: 'No fields to update' }, 400);
  }
  const setClause = fieldsToUpdate.map(key => `${key} = ?`).join(', ');
  const values = fieldsToUpdate.map(key => clientData[key as keyof typeof clientData]);

  try {
    const result = await db.prepare(`UPDATE clients SET ${setClause} WHERE id = ? AND company_id = ?`)
                            .bind(...values, clientId, user.company_id)
                            .run();

    if (result.meta.changes === 0) {
        return c.json({ error: 'Client not found, no changes made, or access denied' }, 404);
    }

    const updatedClient = await db.prepare('SELECT * FROM clients WHERE id = ? AND company_id = ?')
                                .bind(clientId, user.company_id)
                                .first();
    return c.json(updatedClient);
  } catch (error: any) {
    console.error(`Error updating client ${clientId}:`, error);
    return c.json({ error: 'Failed to update client', details: error.message }, 500);
  }
});

// Delete a client (consider soft delete: set is_active = false)
clientRoutes.delete('/:clientId', async (c) => {
  const clientId = parseInt(c.req.param('clientId'));
  if (isNaN(clientId)) {
    return c.json({ error: 'Invalid client ID' }, 400);
  }
  const db = c.env.DB;
  const user = c.get('user');

  if (!user || !user.company_id) {
    return c.json({ error: 'User not associated with a company' }, 401);
  }

  try {
    // Check if client has invoices before deleting - prevent orphan records or handle cascade
    const invoices = await db.prepare('SELECT id FROM invoices WHERE client_id = ? AND company_id = ? LIMIT 1')
                             .bind(clientId, user.company_id)
                             .first();
    if (invoices) {
      return c.json({ error: 'Cannot delete client with existing invoices. Please reassign or delete invoices first.' }, 400);
    }

    const result = await db.prepare('DELETE FROM clients WHERE id = ? AND company_id = ?')
                            .bind(clientId, user.company_id)
                            .run();

    if (result.meta.changes === 0) {
        return c.json({ error: 'Client not found or access denied' }, 404);
    }

    return c.json({ message: 'Client deleted successfully' });
  } catch (error: any) {
    console.error(`Error deleting client ${clientId}:`, error);
    return c.json({ error: 'Failed to delete client', details: error.message }, 500);
  }
});

export default clientRoutes;