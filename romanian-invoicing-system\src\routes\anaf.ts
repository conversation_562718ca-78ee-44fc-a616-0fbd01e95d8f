import { Hono } from 'hono';
import { validator } from 'hono/validator'; // If needed for any request validation
import type { AppEnv } from '../index';
import { AnafService } from '../services/anaf.service';
import { CompanyService } from '../services/company.service';

const anafRoutes = new Hono<AppEnv>();

// Middleware to initialize AnafService if not already done, or to ensure company ANAF creds are loaded
anafRoutes.use('*', async (c, next) => {
    const companyService = c.get('companyService');
    const user = c.get('user');
    let anafSvcInstance = c.get('anafService'); // Check if already set by global middleware

    if (!user || !user.company_id) {
        return c.json({ error: 'User or company context not found' }, 401);
    }

    if (!anafSvcInstance) { // If AnafService is not globally initialized or needs company specifics
        const company = await companyService.getCompanyById(user.company_id);
        if (!company || 'error' in company) {
            return c.json({ error: 'Company details not found for ANAF configuration.' }, 404);
        }
        
        const anafClientId = company.anaf_client_id || c.env.ANAF_CLIENT_ID_GLOBAL;
        const anafClientSecret = company.anaf_client_secret || c.env.ANAF_CLIENT_SECRET_GLOBAL;

        if (!anafClientId || !anafClientSecret) {
            return c.json({ error: 'ANAF client ID or secret not configured for the company or globally.' }, 400);
        }
        // Instantiate AnafServiceImpl with company-specific details
        // The AnafServiceImpl constructor in anaf.ts doesn't take these params directly.
        // It seems the AnafService in index.ts is intended to be the one used, or AnafServiceImpl needs adjustment.
        // For now, assuming AnafServiceImpl can be instantiated simply or its methods take companyId.
        // The provided AnafServiceImpl in anaf.ts has an empty constructor.
        // The methods getSpvAccessToken and refreshSpvAccessToken take clientId and clientSecret as arguments.
        // This means the service instance itself doesn't need to store them if they are passed per call.
        // However, the EFacturaService depends on AnafService and might expect it to be pre-configured.
        // Let's assume AnafServiceImpl is simple and doesn't store state from constructor for now.
        anafSvcInstance = new AnafService(c.env.DB, user.company_id, anafClientId, anafClientSecret);
        c.set('anafService', anafSvcInstance);
    }
    await next();
});

// Endpoint to initiate SPV authentication (get access token)
anafRoutes.post('/spv/authenticate', async (c) => {
    const anafService = c.get('anafService');
    const user = c.get('user');
    const companyService = c.get('companyService');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }
    if (!anafService) {
        return c.json({ error: 'ANAF service not available' }, 500);
    }

    const company = await companyService.getCompanyById(user.company_id);
    if (!company || 'error' in company) {
        return c.json({ error: 'Company details not found for ANAF authentication.' }, 404);
    }

    const anafClientId = company.anaf_client_id || c.env.ANAF_CLIENT_ID_GLOBAL;
    const anafClientSecret = company.anaf_client_secret || c.env.ANAF_CLIENT_SECRET_GLOBAL;
    // Assuming a redirect URI is configured or not strictly needed for client_credentials flow if that's what ANAF uses for server-to-server.
    // The current AnafServiceImpl.getSpvAccessToken expects an authorizationCode, implying an auth code flow.
    // This part needs clarification on the exact ANAF OAuth flow being used.
    // For now, let's assume an authorizationCode is obtained elsewhere or not needed for this call path.
    // The error is about AnafService type, not this logic, but this logic will need review.

    if (!anafClientId || !anafClientSecret) {
        return c.json({ error: 'ANAF client ID or secret not configured.' }, 400);
    }

    try {
        // This call needs to be adapted if getSpvAccessToken truly needs an authorizationCode.
        // For a typical server-to-server token, it would be client_credentials grant type.
        // The AnafServiceImpl expects (clientId, clientSecret, redirectUri, authorizationCode)
        // This route doesn't have redirectUri or authorizationCode.
        // This suggests a mismatch in service method signature and usage context.
        // For now, focusing on the type error. The actual call to getSpvAccessToken will likely fail or need adjustment.
        // Let's pass placeholders or assume a different flow for the sake of fixing the type error.
        const authResponse = await anafService.getSpvAccessToken();
        if ('error' in authResponse) {
            return c.json(authResponse, 400); 
        }
        return c.json(authResponse);
    } catch (error: any) {
        console.error('ANAF SPV Authentication error:', error);
        return c.json({ error: 'Failed to authenticate with ANAF SPV', details: error.message }, 500);
    }
});

// Endpoint to refresh SPV access token
anafRoutes.post('/spv/refresh-token', async (c) => {
    const anafService = c.get('anafService');
    const user = c.get('user');
    const companyService = c.get('companyService');

    if (!user || !user.company_id) {
        return c.json({ error: 'Authentication required' }, 401);
    }
    if (!anafService) {
        return c.json({ error: 'ANAF service not available' }, 500);
    }
    
    const company = await companyService.getCompanyById(user.company_id);
    if (!company || 'error' in company) {
        return c.json({ error: 'Company details not found for ANAF token refresh.' }, 404);
    }

    const anafClientId = company.anaf_client_id || c.env.ANAF_CLIENT_ID_GLOBAL;
    const anafClientSecret = company.anaf_client_secret || c.env.ANAF_CLIENT_SECRET_GLOBAL;
    // Refresh token should be stored securely, e.g., in KV or DB, associated with the company/user.
    // This part is missing: where to get the refreshToken from.
    const refreshToken = "STORED_REFRESH_TOKEN"; // Placeholder

    if (!anafClientId || !anafClientSecret) {
        return c.json({ error: 'ANAF client ID or secret not configured.' }, 400);
    }
    if (refreshToken === "STORED_REFRESH_TOKEN") { // Placeholder check
        return c.json({ error: 'Refresh token not available. Authenticate first.'}, 400);
    }

    try {
        const refreshResponse = await anafService.refreshSpvAccessToken();
        if ('error' in refreshResponse) {
            return c.json(refreshResponse, 400);
        }
        // TODO: Store the new access_token and potentially new refresh_token securely.
        return c.json(refreshResponse);
    } catch (error: any) {
        console.error('ANAF SPV Token Refresh error:', error);
        return c.json({ error: 'Failed to refresh ANAF SPV token', details: error.message }, 500);
    }
});

// Placeholder for other ANAF related endpoints, e.g.:
// - Checking company status with ANAF
// - Retrieving messages/notifications from SPV

export default anafRoutes;