import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { z } from 'zod';

type Bindings = {
  RESEND_API_KEY: string;
  DB: D1Database;
  JWT_SECRET: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'https://*.workers.dev', 'https://*.handmadein.ro'],
  allowMethods: ['GET', 'POST', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// Validation schema
const contactFormSchema = z.object({
  name: z.string().min(1, 'Numele este obligatoriu'),
  email: z.string().email('Adresa de email nu este validă'),
  message: z.string().min(10, 'Mesajul trebuie să aibă cel puțin 10 caractere'),
  phone: z.string().optional(),
  subject: z.string().optional()
});

// Helper functions
function handleError(c: any, error: any, message = 'Internal server error') {
  console.error(message + ':', error);
  return c.json({ success: false, error: message }, 500);
}

/**
 * Submit contact form
 * POST /contact
 */
app.post('/', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate input
    const validation = contactFormSchema.safeParse(body);
    if (!validation.success) {
      return c.json({
        success: false,
        error: 'Datele formularului nu sunt valide',
        details: validation.error.errors
      }, 400);
    }

    const { name, email, message, phone, subject } = validation.data;

    // Check if RESEND_API_KEY is configured
    if (!c.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY not configured');
      return c.json({
        success: false,
        error: 'Serviciul de email nu este configurat corect'
      }, 500);
    }

    // Send email via Resend
    const emailPayload = {
      from: 'Contact Form <<EMAIL>>',
      to: ['<EMAIL>'], // Update with your actual contact email
      subject: subject || 'Mesaj nou din formularul de contact',
      html: generateContactEmailHtml(name, email, message, phone),
      text: generateContactEmailText(name, email, message, phone),
      reply_to: email
    };

    console.log('Sending contact form email via Resend...');
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${c.env.RESEND_API_KEY}`,
      },
      body: JSON.stringify(emailPayload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Failed to send email via Resend:', errorData);
      
      return c.json({
        success: false,
        error: 'A apărut o eroare la trimiterea mesajului. Te rugăm să încerci din nou.'
      }, 500);
    }

    const emailResult = await response.json() as { id?: string };
    console.log('Email sent successfully via Resend:', emailResult);

    // Optionally, store the contact form submission in the database
    if (c.env.DB) {
      try {
        await c.env.DB.prepare(`
          INSERT INTO contact_submissions (name, email, phone, subject, message, created_at, email_id)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          name,
          email,
          phone || null,
          subject || null,
          message,
          new Date().toISOString(),
          emailResult.id || null
        ).run();
        
        console.log('Contact form submission stored in database');
      } catch (dbError) {
        console.error('Failed to store contact form submission in database:', dbError);
        // Don't fail the request if database storage fails
      }
    }

    return c.json({
      success: true,
      message: 'Mesajul tău a fost trimis cu succes. Îți vom răspunde în cel mai scurt timp!',
      id: emailResult.id
    });

  } catch (error) {
    return handleError(c, error, 'Error processing contact form');
  }
});

/**
 * Generate HTML email content for contact form
 */
function generateContactEmailHtml(name: string, email: string, message: string, phone?: string): string {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mesaj nou din formularul de contact</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #4d7c0f;
    }
    h1 {
      color: #4d7c0f;
      margin-top: 0;
      font-size: 24px;
    }
    .content {
      background-color: #f9fafb;
      padding: 20px;
      border-radius: 6px;
      margin: 20px 0;
    }
    .field {
      margin-bottom: 15px;
    }
    .field-label {
      font-weight: bold;
      color: #4d7c0f;
      margin-bottom: 5px;
    }
    .field-value {
      background-color: #ffffff;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #e5e7eb;
    }
    .message-field {
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📧 Mesaj nou din formularul de contact</h1>
      <p>Hanmade in RO</p>
    </div>
    
    <div class="content">
      <div class="field">
        <div class="field-label">👤 Nume:</div>
        <div class="field-value">${escapeHtml(name)}</div>
      </div>
      
      <div class="field">
        <div class="field-label">📧 Email:</div>
        <div class="field-value">
          <a href="mailto:${escapeHtml(email)}">${escapeHtml(email)}</a>
        </div>
      </div>
      
      ${phone ? `
      <div class="field">
        <div class="field-label">📱 Telefon:</div>
        <div class="field-value">
          <a href="tel:${escapeHtml(phone)}">${escapeHtml(phone)}</a>
        </div>
      </div>
      ` : ''}
      
      <div class="field">
        <div class="field-label">💬 Mesaj:</div>
        <div class="field-value message-field">${escapeHtml(message)}</div>
      </div>
    </div>
    
    <div class="footer">
      <p>Acest email a fost generat automat de formularul de contact de pe Hanmade in RO</p>
      <p>Data: ${new Date().toLocaleString('ro-RO', { timeZone: 'Europe/Bucharest' })}</p>
    </div>
  </div>
</body>
</html>`;
}

/**
 * Generate plain text email content for contact form
 */
function generateContactEmailText(name: string, email: string, message: string, phone?: string): string {
  return `MESAJ NOU DIN FORMULARUL DE CONTACT - Hanmade in RO

Nume: ${name}
Email: ${email}
${phone ? `Telefon: ${phone}\n` : ''}
Mesaj:
${message}

---
Acest email a fost generat automat de formularul de contact de pe Hanmade in RO
Data: ${new Date().toLocaleString('ro-RO', { timeZone: 'Europe/Bucharest' })}`;
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text: string): string {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

export default app; 